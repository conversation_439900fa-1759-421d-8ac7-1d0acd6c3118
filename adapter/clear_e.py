import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import collections
from collections import deque
from typing import Optional, Dict, Any

from adapter.proceed import Proceed, add_adapters_
from adapter.module.generator import AdaptGenerator, Bottleneck
from adapter.module.down_up import Down_Up
from adapter.module.base import Adaptation
import transformers


class Transpose(nn.Module):
    def __init__(self, *dims):
        super().__init__()
        self.dims = dims

    def forward(self, x):
        return x.transpose(*self.dims)


class EnergyMetadataEncoder(nn.Module):
    """
    Encodes energy-specific metadata like weather, time-of-day, day-type
    """
    def __init__(self, metadata_dim: int, hidden_dim: int, output_dim: int):
        super().__init__()
        self.metadata_dim = metadata_dim
        self.encoder = nn.Sequential(
            nn.Linear(metadata_dim, hidden_dim),
            nn.GELU(),
            nn.Linear(hidden_dim, output_dim),
            nn.GELU(),
            nn.Linear(output_dim, output_dim)
        )
        
    def forward(self, metadata):
        """
        Args:
            metadata: [batch_size, seq_len, metadata_dim] or [batch_size, metadata_dim]
        Returns:
            encoded: [batch_size, output_dim]
        """
        if metadata.dim() == 3:
            # Take mean over sequence length if 3D
            metadata = metadata.mean(dim=1)
        return self.encoder(metadata)


class LightweightAdaptGenerator(nn.Module):
    """
    Lightweight adaptation generator that only affects final output layers
    """
    def __init__(self, backbone: nn.Module, concept_features: int,
                 activation=nn.LeakyReLU, mid_dim: int = None, target_layers: list = None):
        super().__init__()
        self.concept_features = concept_features
        self.target_layers = target_layers or ['projection', 'head', 'output', 'fc']

        # Use same structure as PROCEED's AdaptGenerator but filter for target layers
        self.dim_name_dict = collections.defaultdict(list)
        self.bottlenecks = nn.ModuleDict()

        # Find target layers and build dim_name_dict like PROCEED
        for name, module in backbone.named_modules():
            if isinstance(module, Adaptation):
                layer_name = name.split('.')[-1].lower()
                # Only include target layers
                if any(target in layer_name for target in self.target_layers):
                    _weight = module.affine_weight if hasattr(module, 'affine_weight') else module.weight
                    out_features = _weight.shape[1 if isinstance(module, transformers.Conv1D) else 0]
                    if _weight.dim() == 1:
                        in_features = 1
                    else:
                        in_features = _weight.shape[0 if isinstance(module, transformers.Conv1D) else 1]
                    out_dim = out_features
                    if module.bias is not None:
                        out_dim += out_features
                    if isinstance(module, Down_Up):
                        out_dim += in_features
                    # Use same key format as PROCEED
                    key = name.split('.')[-1] + '_' + str(out_dim)
                    self.dim_name_dict[key].append(name)

        # Create bottlenecks for target layers only
        for key, names in self.dim_name_dict.items():
            out_dim = int(key.split('_')[-1])
            _hid_dims = min(mid_dim or 64, out_dim // 4)
            self.bottlenecks[key] = Bottleneck(
                concept_features, out_dim, len(names), _hid_dims, activation,
                shared=True, need_bias=True
            )

    def forward(self, concept_vector):
        """Generate adaptations only for target layers"""
        adaptations = {k: bottleneck(concept_vector) for k, bottleneck in self.bottlenecks.items()}
        return adaptations


class DriftMemoryModule(nn.Module):
    """
    Maintains a buffer of recent drift vectors and applies regularization
    """
    def __init__(self, concept_dim: int, memory_size: int = 10, reg_weight: float = 0.1):
        super().__init__()
        self.concept_dim = concept_dim
        self.memory_size = memory_size
        self.reg_weight = reg_weight
        
        # Use deque for efficient FIFO operations
        self.drift_memory = deque(maxlen=memory_size)
        self.register_buffer('memory_tensor', torch.zeros(memory_size, concept_dim))
        self.register_buffer('memory_ptr', torch.tensor(0, dtype=torch.long))
        self.register_buffer('memory_full', torch.tensor(False, dtype=torch.bool))
    
    def update_memory(self, drift_vector):
        """Add new drift vector to memory"""
        if drift_vector.dim() > 1:
            drift_vector = drift_vector.mean(0)  # Average over batch if needed
            
        ptr = self.memory_ptr.item()
        self.memory_tensor[ptr] = drift_vector.detach()
        self.memory_ptr = (self.memory_ptr + 1) % self.memory_size
        
        if ptr == self.memory_size - 1:
            self.memory_full = torch.tensor(True, dtype=torch.bool)
    
    def get_smoothness_loss(self, current_drift):
        """Compute regularization loss for smooth drift evolution"""
        if not self.memory_full and self.memory_ptr == 0:
            return torch.tensor(0.0, device=current_drift.device)
        
        if current_drift.dim() > 1:
            current_drift = current_drift.mean(0)
        
        # Get valid memory entries
        if self.memory_full:
            valid_memory = self.memory_tensor
        else:
            valid_memory = self.memory_tensor[:self.memory_ptr]
        
        if len(valid_memory) == 0:
            return torch.tensor(0.0, device=current_drift.device)
        
        # Compute smoothness as variance of consecutive differences
        recent_drift = valid_memory[-1]  # Most recent drift
        diff = current_drift - recent_drift
        smoothness_loss = torch.norm(diff, p=2)
        
        return self.reg_weight * smoothness_loss


class EnergyAwareLoss(nn.Module):
    """
    Asymmetric loss that penalizes underestimation more during high-load periods
    """
    def __init__(self, base_criterion=nn.MSELoss(), high_load_threshold: float = 0.8, 
                 underestimate_penalty: float = 2.0):
        super().__init__()
        self.base_criterion = base_criterion
        self.high_load_threshold = high_load_threshold
        self.underestimate_penalty = underestimate_penalty
    
    def forward(self, predictions, targets):
        """
        Args:
            predictions: [batch_size, pred_len, features]
            targets: [batch_size, pred_len, features]
        """
        base_loss = self.base_criterion(predictions, targets)
        
        # Identify high-load periods (normalize targets to [0,1] for threshold)
        target_normalized = (targets - targets.min()) / (targets.max() - targets.min() + 1e-8)
        high_load_mask = target_normalized > self.high_load_threshold
        
        # Identify underestimation (prediction < target)
        underestimate_mask = predictions < targets
        
        # Apply penalty for underestimation during high-load periods
        penalty_mask = high_load_mask & underestimate_mask
        if penalty_mask.any():
            penalty_loss = F.mse_loss(predictions[penalty_mask], targets[penalty_mask])
            total_loss = base_loss + (self.underestimate_penalty - 1.0) * penalty_loss
        else:
            total_loss = base_loss
            
        return total_loss


class ClearE(nn.Module):
    """
    CLEAR-E: Concept-aware Lightweight Energy Adaptation for Robust Forecasting

    Key differences from PROCEED:
    1. Energy-specific concept encoder with metadata integration
    2. Lightweight adaptation (only final layers)
    3. Drift memory module with regularization
    4. Energy-aware loss function
    """
    def __init__(self, backbone, args):
        super().__init__()
        self.args = args

        # Freeze backbone if specified
        if args.freeze:
            backbone.requires_grad_(False)

        # Add adapters to backbone (but we'll only use lightweight adaptation)
        self.backbone = add_adapters_(backbone, args)
        self.more_bias = not args.freeze

        # Energy-specific metadata encoder
        metadata_dim = getattr(args, 'metadata_dim', 10)  # weather + calendar features
        metadata_hidden = getattr(args, 'metadata_hidden_dim', 32)

        self.metadata_encoder = EnergyMetadataEncoder(
            metadata_dim=metadata_dim,
            hidden_dim=metadata_hidden,
            output_dim=args.concept_dim
        )

        # Lightweight adaptation generator (only final layers)
        target_layers = getattr(args, 'target_layers', ['projection', 'head', 'output', 'fc'])
        self.lightweight_generator = LightweightAdaptGenerator(
            backbone=backbone,
            concept_features=args.concept_dim,
            activation=nn.Sigmoid if args.act == 'sigmoid' else nn.Identity,
            mid_dim=args.bottleneck_dim,
            target_layers=target_layers
        )

        # Drift memory module
        memory_size = getattr(args, 'drift_memory_size', 10)
        reg_weight = getattr(args, 'drift_reg_weight', 0.1)
        self.drift_memory = DriftMemoryModule(
            concept_dim=args.concept_dim,
            memory_size=memory_size,
            reg_weight=reg_weight
        )

        # Concept encoders for time series data (similar to PROCEED)
        self.mlp1 = nn.Sequential(
            Transpose(-1, -2),
            nn.Linear(args.seq_len, args.concept_dim),
            nn.GELU(),
            nn.Linear(args.concept_dim, args.concept_dim)
        )
        self.mlp2 = nn.Sequential(
            Transpose(-1, -2),
            nn.Linear(args.seq_len + args.pred_len, args.concept_dim),
            nn.GELU(),
            nn.Linear(args.concept_dim, args.concept_dim)
        )

        # Recent batch buffer
        self.register_buffer(
            'recent_batch',
            torch.zeros(1, args.seq_len + args.pred_len, args.enc_in),
            persistent=False
        )

        # EMA for concept tracking
        self.ema = args.ema
        if args.ema > 0:
            self.register_buffer('recent_concept', None, persistent=True)

        # Flags for different modes
        self.flag_online_learning = False
        self.flag_update = False
        self.flag_current = False
        self.flag_basic = False

    def generate_adaptation(self, x, metadata=None):
        """
        Generate adaptations using energy-aware concept encoding

        Args:
            x: Time series input [batch_size, seq_len, features]
            metadata: Energy metadata [batch_size, metadata_dim] or [batch_size, seq_len, metadata_dim]
        """
        # Time series concept encoding (similar to PROCEED)
        ts_concept = self.mlp1(x).mean(-2)  # [batch_size, concept_dim]

        # Recent concept from buffer
        recent_concept = self.mlp2(self.recent_batch).mean(-2).mean(
            list(range(0, self.recent_batch.dim() - 2))
        )

        # Apply EMA if enabled
        if self.ema > 0:
            if self.recent_concept is not None:
                recent_concept = self.recent_concept * self.ema + recent_concept * (1 - self.ema)
            if self.flag_update or self.flag_online_learning and not self.flag_current:
                self.recent_concept = recent_concept.detach()

        # Energy-specific concept enhancement
        if metadata is not None:
            metadata_concept = self.metadata_encoder(metadata)  # [batch_size, concept_dim]
            # Combine time series and metadata concepts
            enhanced_concept = ts_concept + 0.5 * metadata_concept  # Weighted combination
        else:
            enhanced_concept = ts_concept

        # Compute drift
        drift = enhanced_concept - recent_concept

        # Update drift memory and get regularization loss
        if self.training:
            self.drift_memory.update_memory(drift)

        # Generate lightweight adaptations (only for target layers)
        adaptations = self.lightweight_generator(drift)

        return adaptations, drift

    def get_drift_regularization_loss(self, current_drift):
        """Get smoothness regularization loss from drift memory"""
        return self.drift_memory.get_smoothness_loss(current_drift)

    def forward(self, *x, metadata=None):
        """
        Forward pass with optional metadata

        Args:
            x: Time series inputs
            metadata: Optional energy metadata
        """
        if self.flag_basic:
            # Basic mode - use default adaptations like PROCEED
            adaptations = {}
            for i, (k, adapter) in enumerate(self.lightweight_generator.bottlenecks.items()):
                adaptations[k] = adapter.biases[-1] if adapter.need_bias else [None] * len(self.lightweight_generator.dim_name_dict[k])
            drift = None
        else:
            # Generate energy-aware adaptations
            adaptations, drift = self.generate_adaptation(x[0], metadata)
            # Store drift for regularization loss computation
            self._last_drift = drift

        # Apply adaptations using PROCEED's logic but only for target layers
        for out_dim, adaptation in adaptations.items():
            for i in range(len(adaptation)):
                name = self.lightweight_generator.dim_name_dict[out_dim][i]
                self.backbone.get_submodule(name).assign_adaptation(adaptation[i])

        # Forward through backbone
        if self.args.do_predict:
            return self.backbone(*x)
        else:
            return self.backbone(*x)

    def freeze_adapter(self, freeze: bool):
        """Freeze/unfreeze adapter parameters"""
        for param in self.lightweight_generator.parameters():
            param.requires_grad = not freeze
        for param in self.metadata_encoder.parameters():
            param.requires_grad = not freeze
        for param in self.drift_memory.parameters():
            param.requires_grad = not freeze

    def freeze_bias(self, freeze: bool):
        """Freeze/unfreeze bias parameters"""
        for name, module in self.backbone.named_modules():
            if hasattr(module, 'bias') and module.bias is not None:
                module.bias.requires_grad = not freeze
