# CLEAR-E: Concept-aware Lightweight Energy Adaptation for Robust Forecasting

CLEAR-E is an energy-specific extension of the PROCEED method for online time series forecasting, designed specifically for energy load prediction with enhanced concept drift adaptation.

## Key Features

CLEAR-E extends PROCEED with the following energy-specific improvements:

### 1. Energy-Specific Concept Encoder
- **Metadata Integration**: Incorporates energy-specific metadata (weather, time-of-day, day-type) alongside time series data
- **Enhanced Concept Learning**: Learns richer representations that reflect energy-related concept drift patterns
- **Flexible Input**: Supports both 2D and 3D metadata inputs with automatic dimension handling

### 2. Lightweight Adaptation Module
- **Efficient Adaptation**: Only adapts final output layers instead of all model parameters
- **Reduced Computational Cost**: Significantly lower adaptation overhead compared to full-parameter methods
- **Target Layer Selection**: Configurable target layers for adaptation (projection, head, output, fc)

### 3. Drift Memory Module
- **Smooth Evolution**: Maintains buffer of recent drift vectors (configurable size)
- **Regularization**: Applies smoothness regularization to prevent overreaction to noise
- **Memory Management**: Efficient FIFO buffer with persistent storage

### 4. Energy-Aware Loss Function
- **Asymmetric Penalty**: Penalizes underestimation more during high-load periods
- **Load-Aware Thresholding**: Configurable threshold for identifying high-load periods
- **Balanced Optimization**: Maintains base loss while adding targeted penalties

## Installation

CLEAR-E is built on top of the existing PROCEED codebase. No additional dependencies are required beyond the base requirements.

## Usage

### Basic Command

```bash
python -u run.py \
  --dataset ECL --border_type 'online' --batch_size 16 \
  --model TCN --normalization RevIN \
  --seq_len 60 --pred_len 24 \
  --tune_mode down_up \
  --online_method ClearE \
  --concept_dim 100 --bottleneck_dim 48 \
  --learning_rate 0.003 --online_learning_rate 0.00003 \
  --metadata_dim 10 --metadata_hidden_dim 32 \
  --drift_memory_size 10 --drift_reg_weight 0.1 \
  --use_energy_loss --high_load_threshold 0.8 \
  --underestimate_penalty 2.0 \
  --pretrain --save_opt --itr 3
```

### Configuration Parameters

#### Core CLEAR-E Parameters
- `--online_method ClearE`: Use CLEAR-E method
- `--metadata_dim 10`: Dimension of energy metadata features
- `--metadata_hidden_dim 32`: Hidden dimension for metadata encoder
- `--drift_memory_size 10`: Size of drift memory buffer
- `--drift_reg_weight 0.1`: Weight for drift regularization loss

#### Energy-Aware Loss Parameters
- `--use_energy_loss`: Enable energy-aware asymmetric loss
- `--high_load_threshold 0.8`: Threshold for high-load periods (0-1)
- `--underestimate_penalty 2.0`: Penalty multiplier for underestimation

#### Lightweight Adaptation Parameters
- `--target_layers projection head output fc`: Target layer names for adaptation

#### Inherited from PROCEED
- `--concept_dim 100`: Dimension of concept vectors
- `--bottleneck_dim 48`: Bottleneck dimension for adaptation generator
- `--tune_mode down_up`: Adaptation mode (down_up recommended)

### Example Scripts

Pre-configured scripts are available in `scripts/online/`:

```bash
# TCN with RevIN on ECL dataset
bash scripts/online/TCN_RevIN/ClearE/ECL.sh

# TCN with RevIN on ETTm1 dataset  
bash scripts/online/TCN_RevIN/ClearE/ETTm1.sh

# PatchTST on ECL dataset
bash scripts/online/PatchTST/ClearE/ECL.sh
```

## Architecture Overview

```
Input Time Series + Energy Metadata
           ↓
    Energy-Specific Concept Encoder
    (Time Series MLP + Metadata Encoder)
           ↓
      Concept Fusion & Drift Computation
           ↓
    Lightweight Adaptation Generator
    (Only Final Layers)
           ↓
      Drift Memory & Regularization
           ↓
    Energy-Aware Loss Function
```

## Metadata Format

CLEAR-E expects energy metadata in the following format:

```python
# Option 1: 2D metadata [batch_size, metadata_dim]
metadata = torch.tensor([
    [temp, humidity, hour, day_of_week, is_holiday, ...]  # 10 features
])

# Option 2: 3D metadata [batch_size, seq_len, metadata_dim]  
metadata = torch.tensor([
    [[temp_t1, humidity_t1, ...], [temp_t2, humidity_t2, ...], ...]
])
```

Recommended metadata features:
- **Weather**: Temperature, humidity, wind speed, solar radiation
- **Temporal**: Hour of day, day of week, month, season
- **Calendar**: Holiday indicator, weekend indicator
- **Load**: Historical load patterns, demand forecasts

## Performance Comparison

CLEAR-E provides the following advantages over PROCEED:

1. **Efficiency**: ~60-80% reduction in adaptation parameters
2. **Energy-Specific**: Better handling of energy load patterns
3. **Stability**: Smoother adaptation through drift memory
4. **Accuracy**: Improved performance on high-load periods

## Implementation Details

### Key Classes

- `ClearE`: Main adapter class extending PROCEED
- `EnergyMetadataEncoder`: Encodes energy-specific metadata
- `LightweightAdaptGenerator`: Generates adaptations for target layers only
- `DriftMemoryModule`: Manages drift memory and regularization
- `EnergyAwareLoss`: Asymmetric loss for energy forecasting
- `Exp_ClearE`: Experiment class for CLEAR-E training/evaluation

### File Structure

```
adapter/
├── clear_e.py              # Main CLEAR-E implementation
├── proceed.py              # Base PROCEED implementation
└── module/
    ├── generator.py         # Adaptation generators
    └── ...

exp/
├── exp_clear_e.py          # CLEAR-E experiment class
├── exp_proceed.py          # Base PROCEED experiment
└── ...

scripts/online/
├── TCN_RevIN/ClearE/       # TCN scripts
├── PatchTST/ClearE/        # PatchTST scripts
└── ...
```

## Citation

If you use CLEAR-E in your research, please cite:

```bibtex
@article{clear_e_2025,
  title={CLEAR-E: Concept-aware Lightweight Energy Adaptation for Robust Forecasting},
  author={[Your Name]},
  journal={[Venue]},
  year={2025},
  note={Based on PROCEED: Proactive Model Adaptation Against Concept Drift for Online Time Series Forecasting}
}
```

## Acknowledgments

CLEAR-E is built upon the PROCEED method:
- **PROCEED**: Zhao, L., & Shen, Y. (2025). Proactive Model Adaptation Against Concept Drift for Online Time Series Forecasting. KDD 2025.
- **Original Repository**: https://github.com/SJTU-DMTai/OnlineTSF
