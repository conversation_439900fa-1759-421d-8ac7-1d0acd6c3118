This is BibTeX, Version 0.99d (TeX Live 2023/Debian)
Capacity: max_strings=200000, hash_size=200000, hash_prime=170003
The top-level auxiliary file: paper.aux
The style file: IEEEtran.bst
Reallocated singl_function (elt_size=4) to 100 items from 50.
Reallocated singl_function (elt_size=4) to 100 items from 50.
Reallocated singl_function (elt_size=4) to 100 items from 50.
Reallocated wiz_functions (elt_size=4) to 6000 items from 3000.
Reallocated singl_function (elt_size=4) to 100 items from 50.
Database file #1: references.bib
-- IEEEtran.bst version 1.14 (2015/08/26) by <PERSON>.
-- http://www.michaelshell.org/tex/ieeetran/bibtex/
-- See the "IEEEtran_bst_HOWTO.pdf" manual for usage information.

Done.
You've used 26 entries,
            4087 wiz_defined-function locations,
            977 strings with 12937 characters,
and the built_in function-call counts, 23832 in all, are:
= -- 1878
> -- 660
< -- 218
+ -- 377
- -- 135
* -- 1180
:= -- 3342
add.period$ -- 56
call.type$ -- 26
change.case$ -- 26
chr.to.int$ -- 507
cite$ -- 26
duplicate$ -- 1650
empty$ -- 1863
format.name$ -- 141
if$ -- 5600
int.to.chr$ -- 0
int.to.str$ -- 26
missing$ -- 294
newline$ -- 101
num.names$ -- 26
pop$ -- 680
preamble$ -- 1
purify$ -- 0
quote$ -- 2
skip$ -- 1825
stack$ -- 0
substring$ -- 1278
swap$ -- 1423
text.length$ -- 49
text.prefix$ -- 0
top$ -- 5
type$ -- 26
warning$ -- 0
while$ -- 104
width$ -- 28
write$ -- 279
