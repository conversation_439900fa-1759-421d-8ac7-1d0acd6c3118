#!/bin/bash

# Compile CLEAR-E paper
echo "Compiling CLEAR-E paper..."

cd "$(dirname "$0")"

# Check if pdflatex is available
if ! command -v pdflatex &> /dev/null; then
    echo "Error: pdflatex not found. Please install LaTeX."
    exit 1
fi

# Check if bibtex is available
if ! command -v bibtex &> /dev/null; then
    echo "Error: bibtex not found. Please install LaTeX with bibtex support."
    exit 1
fi

# Compile the paper
echo "Running pdflatex (first pass)..."
pdflatex paper.tex

echo "Running bibtex..."
bibtex paper

echo "Running pdflatex (second pass)..."
pdflatex paper.tex

echo "Running pdflatex (final pass)..."
pdflatex paper.tex

# Check if PDF was generated
if [ -f "paper.pdf" ]; then
    echo "✅ Paper compiled successfully: paper.pdf"
    echo "📄 Page count: $(pdfinfo paper.pdf 2>/dev/null | grep Pages | awk '{print $2}' || echo 'Unknown')"
    echo "📊 File size: $(ls -lh paper.pdf | awk '{print $5}')"
else
    echo "❌ Compilation failed. Check the log files for errors."
    exit 1
fi

# Clean up auxiliary files (optional)
rm -f *.aux *.bbl *.blg *.log *.out *.toc *.lof *.lot
echo "🧹 Auxiliary files cleaned up."