@article{hong2016probabilistic,
  title={Probabilistic energy forecasting: Global energy forecasting competition 2014 and beyond},
  author={<PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON>, <PERSON> and <PERSON>, <PERSON>},
  journal={International Journal of Forecasting},
  volume={32},
  number={3},
  pages={896--913},
  year={2016},
  publisher={Elsevier}
}

@article{wang2018review,
  title={A review of deep learning for renewable energy forecasting},
  author={<PERSON>, <PERSON> and <PERSON>, <PERSON><PERSON><PERSON> and <PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON>},
  journal={Energy Conversion and Management},
  volume={198},
  pages={111799},
  year={2019},
  publisher={Elsevier}
}

@article{taylor2003short,
  title={Short-term electricity demand forecasting using double seasonal exponential smoothing},
  author={<PERSON>, <PERSON>},
  journal={Journal of the Operational Research Society},
  volume={54},
  number={8},
  pages={799--805},
  year={2003},
  publisher={<PERSON> \& Francis}
}

@article{haben2021review,
  title={Review of low voltage load forecasting: Methods, applications, and recommendations},
  author={<PERSON><PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON><PERSON> and <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON> and Singleton, Corentin},
  journal={Applied Energy},
  volume={304},
  pages={117798},
  year={2021},
  publisher={Elsevier}
}

@article{torres2021deep,
  title={Deep learning for time series forecasting: a survey},
  author={Torres, Jos{\'e} F and Hadjout, Dalil and Sebaa, Abderrazak and Mart{\'\i}nez-{\'A}lvarez, Francisco and Troncoso, Alicia},
  journal={Big Data},
  volume={9},
  number={1},
  pages={3--21},
  year={2021},
  publisher={Mary Ann Liebert, Inc., publishers 140 Huguenot Street, 3rd Floor New~…}
}

@inproceedings{zhou2021informer,
  title={Informer: Beyond efficient transformer for long sequence time-series forecasting},
  author={Zhou, Haoyi and Zhang, Shanghang and Peng, Jieqi and Zhang, Shuai and Li, Jianxin and Xiong, Hui and Zhang, Wancai},
  booktitle={Proceedings of the AAAI Conference on Artificial Intelligence},
  volume={35},
  number={12},
  pages={11106--11115},
  year={2021}
}

@inproceedings{houlsby2019parameter,
  title={Parameter-efficient transfer learning for NLP},
  author={Houlsby, Neil and Giurgiu, Andrei and Jastrzebski, Stanislaw and Morrone, Bruna and De Laroussilhe, Quentin and Gesmundo, Andrea and Attariyan, Mona and Gelly, Sylvain},
  booktitle={International Conference on Machine Learning},
  pages={2790--2799},
  year={2019},
  organization={PMLR}
}

@article{zhao2025proceed,
  title={Proactive Model Adaptation Against Concept Drift for Online Time Series Forecasting},
  author={Zhao, Liang and Shen, Yifan},
  journal={Proceedings of the 31st ACM SIGKDD Conference on Knowledge Discovery and Data Mining},
  year={2025}
}

@article{hippert2001neural,
  title={Neural networks for short-term load forecasting: a review and evaluation},
  author={Hippert, Henrique Steinherz and Pedreira, Carlos Eduardo and Souza, Reinaldo Castro},
  journal={IEEE Transactions on power systems},
  volume={16},
  number={1},
  pages={44--55},
  year={2001},
  publisher={IEEE}
}

@article{contreras2003arima,
  title={ARIMA models to predict next-day electricity prices},
  author={Contreras, Javier and Espinola, Rosario and Nogales, Francisco J and Conejo, Antonio J},
  journal={IEEE transactions on power systems},
  volume={18},
  number={3},
  pages={1014--1020},
  year={2003},
  publisher={IEEE}
}

@article{chen2004load,
  title={Load forecasting using support vector machines: a study on EUNITE competition 2001},
  author={Chen, Bing-Jing and Chang, Ming-Wei and Lin, Chih-Jen},
  journal={IEEE transactions on power systems},
  volume={19},
  number={4},
  pages={1821--1830},
  year={2004},
  publisher={IEEE}
}

@article{shi2018deep,
  title={Deep learning for household load forecasting—a novel pooling deep RNN},
  author={Shi, Heng and Xu, Minghao and Li, Ran},
  journal={IEEE Transactions on Smart Grid},
  volume={9},
  number={5},
  pages={5271--5280},
  year={2017},
  publisher={IEEE}
}

@article{kong2017short,
  title={Short-term residential load forecasting based on LSTM recurrent neural network},
  author={Kong, Weicong and Dong, Zhao Yang and Jia, Youwei and Hill, David J and Xu, Yan and Zhang, Yuan},
  journal={IEEE Transactions on Smart Grid},
  volume={10},
  number={1},
  pages={841--851},
  year={2017},
  publisher={IEEE}
}

@inproceedings{wu2021autoformer,
  title={Autoformer: Decomposition transformers with auto-correlation for long-term series forecasting},
  author={Wu, Haixu and Xu, Jiehui and Wang, Jianmin and Long, Mingsheng},
  booktitle={Advances in Neural Information Processing Systems},
  volume={34},
  pages={22419--22430},
  year={2021}
}

@article{gama2014survey,
  title={A survey on concept drift adaptation},
  author={Gama, Jo{\~a}o and {\v{Z}}liobait{\.e}, Indr{\.e} and Bifet, Albert and Pechenizkiy, Mykola and Bouchachia, Abdelhamid},
  journal={ACM computing surveys},
  volume={46},
  number={4},
  pages={1--37},
  year={2014},
  publisher={ACM New York, NY, USA}
}

@inproceedings{kuncheva2004classifier,
  title={Classifier ensembles for changing environments},
  author={Kuncheva, Ludmila I},
  booktitle={International workshop on multiple classifier systems},
  pages={1--15},
  year={2004},
  organization={Springer}
}

@article{losing2018incremental,
  title={Incremental on-line learning: A review and comparison of state of the art algorithms},
  author={Losing, Viktor and Hammer, Barbara and Wersing, Heiko},
  journal={Neurocomputing},
  volume={275},
  pages={1261--1274},
  year={2018},
  publisher={Elsevier}
}

@inproceedings{gama2004learning,
  title={Learning with drift detection},
  author={Gama, Jo{\~a}o and Medas, Pedro and Castillo, Gladys and Rodrigues, Pedro},
  booktitle={Brazilian symposium on artificial intelligence},
  pages={286--295},
  year={2004},
  organization={Springer}
}

@inproceedings{finn2017model,
  title={Model-agnostic meta-learning for fast adaptation of deep networks},
  author={Finn, Chelsea and Abbeel, Pieter and Levine, Sergey},
  booktitle={International conference on machine learning},
  pages={1126--1135},
  year={2017},
  organization={PMLR}
}

@article{kirkpatrick2017overcoming,
  title={Overcoming catastrophic forgetting in neural networks},
  author={Kirkpatrick, James and Pascanu, Razvan and Rabinowitz, Neil and Veness, Joel and Desjardins, Guillaume and Rusu, Andrei A and Milan, Kieran and Quan, John and Ramalho, Tiago and Grabska-Barwinska, Agnieszka and others},
  journal={Proceedings of the national academy of sciences},
  volume={114},
  number={13},
  pages={3521--3526},
  year={2017},
  publisher={National Acad Sciences}
}

@inproceedings{hu2021lora,
  title={LoRA: Low-rank adaptation of large language models},
  author={Hu, Edward J and Shen, Yelong and Wallis, Phillip and Allen-Zhu, Zeyuan and Li, Yuanzhi and Wang, Shean and Wang, Lu and Chen, Weizhu},
  booktitle={International Conference on Learning Representations},
  year={2022}
}

@inproceedings{lester2021power,
  title={The power of scale for parameter-efficient prompt tuning},
  author={Lester, Brian and Al-Rfou, Rami and Constant, Noah},
  booktitle={Proceedings of the 2021 Conference on Empirical Methods in Natural Language Processing},
  pages={3045--3059},
  year={2021}
}

@inproceedings{nie2022time,
  title={A time series is worth 64 words: Long-term forecasting with transformers},
  author={Nie, Yuqi and Nguyen, Nam H and Sinthong, Phanwadee and Kalagnanam, Jayant},
  booktitle={International Conference on Learning Representations},
  year={2023}
}

@inproceedings{zeng2023transformers,
  title={Are transformers effective for time series forecasting?},
  author={Zeng, Ailing and Chen, Muxi and Zhang, Lei and Xu, Qiang},
  booktitle={Proceedings of the AAAI conference on artificial intelligence},
  volume={37},
  number={9},
  pages={11121--11128},
  year={2023}
}

@inproceedings{bai2018empirical,
  title={An empirical evaluation of generic convolutional and recurrent networks for sequence modeling},
  author={Bai, Shaojie and Kolter, J Zico and Koltun, Vladlen},
  booktitle={arXiv preprint arXiv:1803.01271},
  year={2018}
}

@inproceedings{liu2023itransformer,
  title={iTransformer: Inverted transformers are effective for time series forecasting},
  author={Liu, Yong and Wu, Haixu and Wang, Jianmin and Long, Mingsheng},
  booktitle={International Conference on Learning Representations},
  year={2024}
}
