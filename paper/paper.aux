\relax 
\citation{hong2016probabilistic}
\citation{wang2018review}
\citation{haben2021review}
\citation{taylor2003short}
\citation{torres2021deep,zhou2021informer}
\citation{houlsby2019parameter}
\citation{zhao2025proceed}
\@writefile{toc}{\contentsline {section}{\numberline {I}Introduction}{1}{}\protected@file@percent }
\citation{contreras2003arima}
\citation{taylor2003short}
\citation{hippert2001neural}
\citation{chen2004load}
\citation{shi2018deep}
\citation{kong2017short}
\citation{torres2021deep,zhou2021informer,wu2021autoformer}
\citation{gama2014survey,haben2021review}
\citation{kuncheva2004classifier}
\citation{losing2018incremental}
\citation{gama2004learning}
\citation{finn2017model}
\citation{kirkpatrick2017overcoming}
\citation{houlsby2019parameter}
\citation{hu2021lora}
\citation{lester2021power}
\citation{zhao2025proceed}
\@writefile{toc}{\contentsline {section}{\numberline {II}Related Work}{2}{}\protected@file@percent }
\@writefile{lot}{\contentsline {table}{\numberline {I}{\ignorespaces Mathematical Notation}}{2}{}\protected@file@percent }
\newlabel{tab:notation}{{I}{2}{}{}{}}
\@writefile{toc}{\contentsline {section}{\numberline {III}Methodology}{2}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {III-A}}Notation and Definitions}{2}{}\protected@file@percent }
\newlabel{sec:notation}{{\mbox  {III-A}}{2}{}{}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {III-B}}Problem Formulation and Mathematical Framework}{2}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {III-B}1}Formal Problem Definition}{2}{}\protected@file@percent }
\newlabel{eq:input_series}{{1}{2}{}{}{}}
\newlabel{eq:metadata_series}{{2}{2}{}{}{}}
\newlabel{eq:target_series}{{3}{2}{}{}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {III-B}2}Concept Drift Formalization}{3}{}\protected@file@percent }
\newlabel{eq:concept_drift}{{4}{3}{}{}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {III-B}3}Optimization Objective}{3}{}\protected@file@percent }
\newlabel{eq:optimization_objective}{{5}{3}{}{}{}}
\newlabel{eq:constrained_optimization}{{6}{3}{}{}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {III-C}}CLEAR-E Architecture and Theoretical Framework}{3}{}\protected@file@percent }
\newlabel{eq:clear_e_framework}{{7}{3}{}{}{}}
\newlabel{sec:concept_encoder}{{\mbox  {III-C}1}{3}{}{}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {III-C}1}Energy-specific Concept Encoder}{3}{}\protected@file@percent }
\newlabel{eq:temporal_encoding}{{8}{3}{}{}{}}
\@writefile{lof}{\contentsline {figure}{\numberline {1}{\ignorespaces CLEAR-E Architecture Overview. The framework processes time series and energy metadata through specialized encoders, computes concept drift using exponential moving averages, maintains a memory buffer for smooth adaptation, generates lightweight adaptations for target layers only, and applies energy-aware loss for asymmetric penalty during high-demand periods.}}{3}{}\protected@file@percent }
\newlabel{fig:architecture}{{1}{3}{}{}{}}
\newlabel{eq:feature_weights}{{9}{3}{}{}{}}
\newlabel{eq:weighted_metadata}{{10}{3}{}{}{}}
\newlabel{eq:metadata_encoding}{{11}{3}{}{}{}}
\newlabel{eq:attention}{{12}{3}{}{}{}}
\newlabel{eq:concept_fusion}{{13}{3}{}{}{}}
\newlabel{sec:adaptation_generator}{{\mbox  {III-C}2}{4}{}{}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {III-C}2}Lightweight Adaptation Generator}{4}{}\protected@file@percent }
\newlabel{eq:target_layers}{{14}{4}{}{}{}}
\newlabel{eq:adaptation_generation}{{15}{4}{}{}{}}
\newlabel{eq:bottleneck}{{16}{4}{}{}{}}
\newlabel{eq:adapted_computation}{{17}{4}{}{}{}}
\newlabel{eq:parameter_complexity}{{18}{4}{}{}{}}
\newlabel{sec:drift_memory}{{\mbox  {III-C}3}{4}{}{}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {III-C}3}Enhanced Drift Memory Module}{4}{}\protected@file@percent }
\newlabel{eq:concept_ema}{{19}{4}{}{}{}}
\newlabel{eq:drift_vector}{{20}{4}{}{}{}}
\newlabel{eq:drift_mean}{{21}{4}{}{}{}}
\newlabel{eq:drift_variance}{{22}{4}{}{}{}}
\newlabel{eq:smoothness_loss}{{23}{4}{}{}{}}
\newlabel{sec:energy_loss}{{\mbox  {III-C}4}{5}{}{}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {III-C}4}Energy-aware Loss Function}{5}{}\protected@file@percent }
\newlabel{eq:energy_loss}{{24}{5}{}{}{}}
\newlabel{eq:penalty_term}{{25}{5}{}{}{}}
\newlabel{eq:adaptive_threshold}{{26}{5}{}{}{}}
\newlabel{eq:penalty_weight}{{27}{5}{}{}{}}
\newlabel{eq:energy_gradient}{{28}{5}{}{}{}}
\newlabel{eq:gradient_expanded}{{29}{5}{}{}{}}
\@writefile{lof}{\contentsline {figure}{\numberline {2}{\ignorespaces Energy-aware Loss Function Behavior. The proposed loss function applies higher penalties for under-prediction during high-load periods (right of threshold $\tau $) while maintaining standard behavior for over-prediction and normal-load periods. This asymmetry reflects the operational costs in energy systems where supply shortages are more critical than oversupply.}}{5}{}\protected@file@percent }
\newlabel{fig:energy_loss}{{2}{5}{}{}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {III-D}}Training Procedure}{5}{}\protected@file@percent }
\newlabel{sec:training_procedure}{{\mbox  {III-D}}{5}{}{}{}}
\newlabel{eq:frozen_update}{{30}{5}{}{}{}}
\newlabel{eq:unfrozen_update}{{31}{5}{}{}{}}
\citation{zhao2025proceed}
\citation{nie2022time}
\citation{zeng2023transformers}
\citation{bai2018empirical}
\citation{liu2023itransformer}
\@writefile{loa}{\contentsline {algorithm}{\numberline {1}{\ignorespaces CLEAR-E Core Adaptation Process}}{6}{}\protected@file@percent }
\newlabel{alg:clear_e_core}{{1}{6}{}{}{}}
\@writefile{lof}{\contentsline {figure}{\numberline {3}{\ignorespaces CLEAR-E Algorithm Flowchart. The process begins with data reception, performs energy-specific concept encoding, computes drift vectors, updates memory buffer, detects significant drift events, adapts regularization accordingly, generates lightweight adaptations, and applies energy-aware loss for parameter updates. The cycle repeats for continuous online learning.}}{6}{}\protected@file@percent }
\newlabel{fig:algorithm_flow}{{3}{6}{}{}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {III-E}}Computational Efficiency Analysis}{6}{}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{\numberline {IV}Experiments}{6}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {IV-A}}Experimental Setup}{6}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {IV-A}1}Datasets}{6}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {IV-A}2}Baselines}{6}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {IV-A}3}Evaluation Metrics}{6}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {IV-B}}Main Results}{6}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {IV-C}}Ablation Studies}{6}{}\protected@file@percent }
\@writefile{lot}{\contentsline {table}{\numberline {II}{\ignorespaces Main Results on Energy Forecasting Datasets (Prediction Horizon: 24)}}{7}{}\protected@file@percent }
\newlabel{tab:main_results}{{II}{7}{}{}{}}
\@writefile{lot}{\contentsline {table}{\numberline {III}{\ignorespaces Results Across Different Prediction Horizons on ECL Dataset}}{7}{}\protected@file@percent }
\newlabel{tab:horizons}{{III}{7}{}{}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {IV-C}1}Component Analysis}{7}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {IV-C}2}Efficiency Analysis}{7}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {IV-D}}Interpretability Analysis}{7}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {IV-D}1}Feature Importance Analysis}{7}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {IV-D}2}Drift Detection Analysis}{7}{}\protected@file@percent }
\@writefile{lot}{\contentsline {table}{\numberline {IV}{\ignorespaces Ablation Study on ECL Dataset}}{7}{}\protected@file@percent }
\newlabel{tab:ablation}{{IV}{7}{}{}{}}
\@writefile{lot}{\contentsline {table}{\numberline {V}{\ignorespaces Computational Efficiency Comparison}}{7}{}\protected@file@percent }
\newlabel{tab:efficiency}{{V}{7}{}{}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {IV-E}}Computational Efficiency}{7}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {IV-F}}Sensitivity Analysis}{7}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {IV-F}1}Drift Memory Size}{7}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {IV-F}2}Regularization Weight}{7}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {IV-F}3}Energy Loss Penalty}{7}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {IV-G}}Real-world Deployment Considerations}{7}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {IV-G}1}Data Requirements}{7}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {IV-G}2}Computational Requirements}{7}{}\protected@file@percent }
\bibstyle{IEEEtran}
\bibdata{references}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {IV-G}3}Integration with Energy Management Systems}{8}{}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{\numberline {V}Limitations and Future Work}{8}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {V-A}}Current Limitations}{8}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {V-B}}Future Research Directions}{8}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {V-B}1}Multi-modal Extensions}{8}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {V-B}2}Hierarchical Forecasting}{8}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {V-B}3}Advanced Adaptation Mechanisms}{8}{}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{\numberline {VI}Conclusion}{8}{}\protected@file@percent }
\gdef \@abspage@last{8}
