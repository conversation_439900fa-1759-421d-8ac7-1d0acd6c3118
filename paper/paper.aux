\relax 
\citation{hong2016probabilistic}
\citation{wang2018review}
\citation{taylor2003short}
\citation{haben2021review}
\citation{torres2021deep}
\citation{zhou2021informer}
\citation{houlsby2019parameter}
\citation{zhao2025proceed}
\citation{hippert2001neural}
\citation{contreras2003arima}
\citation{taylor2003short}
\citation{chen2004load}
\@writefile{toc}{\contentsline {section}{\numberline {I}Introduction}{1}{}\protected@file@percent }
\citation{torres2021deep}
\citation{shi2018deep}
\citation{kong2017short}
\citation{zhou2021informer,wu2021autoformer}
\citation{gama2014survey}
\citation{haben2021review}
\citation{kuncheva2004classifier}
\citation{losing2018incremental}
\citation{gama2004learning}
\citation{finn2017model}
\citation{kirkpatrick2017overcoming}
\citation{houlsby2019parameter}
\citation{houlsby2019parameter}
\citation{hu2021lora}
\citation{lester2021power}
\citation{zhao2025proceed}
\@writefile{toc}{\contentsline {section}{\numberline {II}Related Work}{2}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {II-A}}Energy Load Forecasting}{2}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {II-B}}Concept Drift in Time Series}{2}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {II-C}}Parameter-Efficient Fine-Tuning}{2}{}\protected@file@percent }
\@writefile{lot}{\contentsline {table}{\numberline {I}{\ignorespaces Mathematical Notation}}{2}{}\protected@file@percent }
\newlabel{tab:notation}{{I}{2}{}{}{}}
\@writefile{toc}{\contentsline {section}{\numberline {III}Methodology}{2}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {III-A}}Notation and Definitions}{2}{}\protected@file@percent }
\newlabel{sec:notation}{{\mbox  {III-A}}{2}{}{}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {III-B}}Problem Formulation and Mathematical Framework}{2}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {III-B}1}Formal Problem Definition}{2}{}\protected@file@percent }
\newlabel{eq:input_series}{{1}{2}{}{}{}}
\newlabel{eq:metadata_series}{{2}{2}{}{}{}}
\newlabel{eq:target_series}{{3}{2}{}{}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {III-B}2}Concept Drift Formalization}{2}{}\protected@file@percent }
\newlabel{eq:concept_drift}{{4}{2}{}{}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {III-B}3}Optimization Objective}{3}{}\protected@file@percent }
\newlabel{eq:optimization_objective}{{5}{3}{}{}{}}
\newlabel{eq:constrained_optimization}{{6}{3}{}{}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {III-C}}CLEAR-E Architecture and Theoretical Framework}{3}{}\protected@file@percent }
\newlabel{eq:clear_e_framework}{{7}{3}{}{}{}}
\newlabel{sec:concept_encoder}{{\mbox  {III-C}1}{3}{}{}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {III-C}1}Energy-specific Concept Encoder}{3}{}\protected@file@percent }
\newlabel{eq:temporal_encoding}{{8}{3}{}{}{}}
\@writefile{lof}{\contentsline {figure}{\numberline {1}{\ignorespaces CLEAR-E Architecture Overview. The framework processes time series and energy metadata through specialized encoders, computes concept drift using exponential moving averages, maintains a memory buffer for smooth adaptation, generates lightweight adaptations for target layers only, and applies energy-aware loss for asymmetric penalty during high-demand periods.}}{3}{}\protected@file@percent }
\newlabel{fig:architecture}{{1}{3}{}{}{}}
\newlabel{eq:feature_weights}{{9}{3}{}{}{}}
\newlabel{eq:weighted_metadata}{{10}{3}{}{}{}}
\newlabel{eq:metadata_encoding}{{11}{3}{}{}{}}
\newlabel{eq:attention}{{12}{3}{}{}{}}
\newlabel{eq:concept_fusion}{{13}{3}{}{}{}}
\@writefile{loa}{\contentsline {algorithm}{\numberline {1}{\ignorespaces Energy-Specific Concept Encoder}}{4}{}\protected@file@percent }
\newlabel{alg:concept_encoder}{{1}{4}{}{}{}}
\newlabel{sec:adaptation_generator}{{\mbox  {III-C}2}{4}{}{}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {III-C}2}Lightweight Adaptation Generator}{4}{}\protected@file@percent }
\newlabel{eq:layer_sensitivity}{{14}{4}{}{}{}}
\newlabel{eq:target_layers}{{15}{4}{}{}{}}
\newlabel{eq:adaptation_generation}{{16}{4}{}{}{}}
\newlabel{eq:bottleneck}{{17}{4}{}{}{}}
\@writefile{loa}{\contentsline {algorithm}{\numberline {2}{\ignorespaces Lightweight Adaptation Generator}}{4}{}\protected@file@percent }
\newlabel{alg:adaptation_generator}{{2}{4}{}{}{}}
\newlabel{eq:adapted_computation}{{18}{4}{}{}{}}
\newlabel{eq:parameter_complexity}{{19}{4}{}{}{}}
\newlabel{sec:drift_memory}{{\mbox  {III-C}3}{4}{}{}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {III-C}3}Enhanced Drift Memory Module}{4}{}\protected@file@percent }
\newlabel{eq:concept_ema}{{20}{5}{}{}{}}
\newlabel{eq:drift_vector}{{21}{5}{}{}{}}
\newlabel{eq:drift_mean}{{22}{5}{}{}{}}
\newlabel{eq:drift_variance}{{23}{5}{}{}{}}
\newlabel{eq:smoothness_loss}{{24}{5}{}{}{}}
\newlabel{eq:mahalanobis_distance}{{25}{5}{}{}{}}
\newlabel{eq:drift_detection}{{26}{5}{}{}{}}
\newlabel{eq:adaptive_regularization}{{27}{5}{}{}{}}
\@writefile{loa}{\contentsline {algorithm}{\numberline {3}{\ignorespaces Enhanced Drift Memory Module}}{5}{}\protected@file@percent }
\newlabel{alg:drift_memory}{{3}{5}{}{}{}}
\newlabel{sec:energy_loss}{{\mbox  {III-C}4}{5}{}{}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {III-C}4}Energy-aware Loss Function}{5}{}\protected@file@percent }
\newlabel{eq:energy_loss}{{29}{6}{}{}{}}
\newlabel{eq:penalty_term}{{30}{6}{}{}{}}
\newlabel{eq:adaptive_threshold}{{31}{6}{}{}{}}
\newlabel{eq:penalty_weight}{{32}{6}{}{}{}}
\newlabel{eq:energy_gradient}{{33}{6}{}{}{}}
\newlabel{eq:gradient_expanded}{{34}{6}{}{}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {III-D}}CLEAR-E Training Algorithm}{6}{}\protected@file@percent }
\newlabel{sec:training_algorithm}{{\mbox  {III-D}}{6}{}{}{}}
\@writefile{lof}{\contentsline {figure}{\numberline {2}{\ignorespaces Energy-aware Loss Function Behavior. The proposed loss function applies higher penalties for under-prediction during high-load periods (right of threshold $\tau $) while maintaining standard behavior for over-prediction and normal-load periods. This asymmetry reflects the operational costs in energy systems where supply shortages are more critical than oversupply.}}{6}{}\protected@file@percent }
\newlabel{fig:energy_loss}{{2}{6}{}{}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {III-E}}Computational Complexity Analysis}{6}{}\protected@file@percent }
\newlabel{sec:complexity_analysis}{{\mbox  {III-E}}{6}{}{}{}}
\newlabel{eq:time_complexity}{{36}{6}{}{}{}}
\newlabel{eq:space_complexity}{{37}{6}{}{}{}}
\@writefile{lof}{\contentsline {figure}{\numberline {3}{\ignorespaces CLEAR-E Algorithm Flowchart. The process begins with data reception, performs energy-specific concept encoding, computes drift vectors, updates memory buffer, detects significant drift events, adapts regularization accordingly, generates lightweight adaptations, and applies energy-aware loss for parameter updates. The cycle repeats for continuous online learning.}}{7}{}\protected@file@percent }
\newlabel{fig:algorithm_flow}{{3}{7}{}{}{}}
\newlabel{eq:parameter_ratio}{{38}{7}{}{}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {III-F}}Theoretical Properties and Guarantees}{7}{}\protected@file@percent }
\newlabel{sec:theoretical_properties}{{\mbox  {III-F}}{7}{}{}{}}
\newlabel{thm:universal_approximation}{{3}{7}{}{}{}}
\newlabel{thm:adaptation_stability}{{4}{7}{}{}{}}
\newlabel{lem:energy_loss_convexity}{{1}{7}{}{}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {III-G}}Assumptions and Problem Setting}{7}{}\protected@file@percent }
\newlabel{sec:assumptions}{{\mbox  {III-G}}{7}{}{}{}}
\newlabel{ass:bounded_inputs}{{1}{7}{}{}{}}
\newlabel{ass:lipschitz}{{2}{7}{}{}{}}
\newlabel{ass:bounded_drift}{{3}{7}{}{}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {III-H}}Relationship to Existing Methods}{7}{}\protected@file@percent }
\newlabel{sec:relationship_existing}{{\mbox  {III-H}}{7}{}{}{}}
\@writefile{toc}{\contentsline {section}{\numberline {IV}Experiments}{7}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {IV-A}}Experimental Setup}{7}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {IV-A}1}Datasets}{7}{}\protected@file@percent }
\citation{zhao2025proceed}
\citation{nie2022time}
\citation{zeng2023transformers}
\citation{bai2018empirical}
\citation{liu2023itransformer}
\@writefile{lot}{\contentsline {table}{\numberline {II}{\ignorespaces Main Results on Energy Forecasting Datasets (Prediction Horizon: 24)}}{8}{}\protected@file@percent }
\newlabel{tab:main_results}{{II}{8}{}{}{}}
\@writefile{lot}{\contentsline {table}{\numberline {III}{\ignorespaces Results Across Different Prediction Horizons on ECL Dataset}}{8}{}\protected@file@percent }
\newlabel{tab:horizons}{{III}{8}{}{}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {IV-A}2}Baselines}{8}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {IV-A}3}Evaluation Metrics}{8}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {IV-B}}Main Results}{8}{}\protected@file@percent }
\@writefile{lot}{\contentsline {table}{\numberline {IV}{\ignorespaces Ablation Study on ECL Dataset}}{8}{}\protected@file@percent }
\newlabel{tab:ablation}{{IV}{8}{}{}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {IV-C}}Ablation Studies}{8}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {IV-C}1}Component Analysis}{8}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {IV-C}2}Efficiency Analysis}{8}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {IV-D}}Interpretability Analysis}{8}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {IV-D}1}Feature Importance Analysis}{8}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {IV-D}2}Drift Detection Analysis}{8}{}\protected@file@percent }
\@writefile{lot}{\contentsline {table}{\numberline {V}{\ignorespaces Computational Efficiency Comparison}}{9}{}\protected@file@percent }
\newlabel{tab:efficiency}{{V}{9}{}{}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {IV-E}}Computational Efficiency}{9}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {IV-F}}Sensitivity Analysis}{9}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {IV-F}1}Drift Memory Size}{9}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {IV-F}2}Regularization Weight}{9}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {IV-F}3}Energy Loss Penalty}{9}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {IV-G}}Real-world Deployment Considerations}{9}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {IV-G}1}Data Requirements}{9}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {IV-G}2}Computational Requirements}{9}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {IV-G}3}Integration with Energy Management Systems}{9}{}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{\numberline {V}Limitations and Future Work}{9}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {V-A}}Current Limitations}{9}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {V-B}}Future Research Directions}{9}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {V-B}1}Multi-modal Extensions}{9}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {V-B}2}Hierarchical Forecasting}{9}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {V-B}3}Advanced Adaptation Mechanisms}{9}{}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{\numberline {VI}Conclusion}{9}{}\protected@file@percent }
\bibstyle{IEEEtran}
\bibdata{references}
\@writefile{loa}{\contentsline {algorithm}{\numberline {4}{\ignorespaces CLEAR-E Online Training Algorithm}}{10}{}\protected@file@percent }
\newlabel{alg:clear_e}{{4}{10}{}{}{}}
\gdef \@abspage@last{10}
