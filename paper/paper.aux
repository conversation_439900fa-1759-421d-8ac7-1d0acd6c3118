\relax 
\citation{hong2016probabilistic}
\citation{wang2018review}
\citation{haben2021review}
\citation{taylor2003short}
\citation{torres2021deep,zhou2021informer}
\citation{houlsby2019parameter}
\citation{zhao2025proceed}
\@writefile{toc}{\contentsline {section}{\numberline {I}Introduction}{1}{}\protected@file@percent }
\citation{contreras2003arima}
\citation{taylor2003short}
\citation{hippert2001neural}
\citation{chen2004load}
\citation{shi2018deep}
\citation{kong2017short}
\citation{torres2021deep,zhou2021informer,wu2021autoformer}
\citation{gama2014survey,haben2021review}
\citation{kuncheva2004classifier}
\citation{losing2018incremental}
\citation{gama2004learning}
\citation{finn2017model}
\citation{kirkpatrick2017overcoming}
\citation{houlsby2019parameter}
\citation{hu2021lora}
\citation{lester2021power}
\citation{zhao2025proceed}
\@writefile{toc}{\contentsline {section}{\numberline {II}Related Work}{2}{}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{\numberline {III}Methodology}{2}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {III-A}}Problem Formulation}{2}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {III-A}1}Formal Problem Definition}{2}{}\protected@file@percent }
\newlabel{eq:input_series}{{1}{2}{}{}{}}
\newlabel{eq:metadata_series}{{2}{2}{}{}{}}
\newlabel{eq:target_series}{{3}{2}{}{}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {III-A}2}Concept Drift Formalization}{2}{}\protected@file@percent }
\newlabel{eq:concept_drift}{{4}{2}{}{}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {III-A}3}Optimization Objective}{2}{}\protected@file@percent }
\newlabel{eq:optimization_objective}{{5}{2}{}{}{}}
\@writefile{lof}{\contentsline {figure}{\numberline {1}{\ignorespaces CLEAR-E Architecture Overview. The framework processes time series and energy metadata through specialized encoders, computes concept drift using exponential moving averages, maintains a memory buffer for smooth adaptation, generates lightweight adaptations for target layers only, and applies energy-aware loss for asymmetric penalty during high-demand periods.}}{3}{}\protected@file@percent }
\newlabel{fig:architecture}{{1}{3}{}{}{}}
\newlabel{eq:constrained_optimization}{{6}{3}{}{}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {III-B}}CLEAR-E Framework}{3}{}\protected@file@percent }
\newlabel{eq:clear_e_framework}{{7}{3}{}{}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {III-B}1}Energy-specific Concept Encoder}{3}{}\protected@file@percent }
\newlabel{eq:temporal_encoding}{{8}{3}{}{}{}}
\newlabel{eq:feature_weights}{{9}{3}{}{}{}}
\newlabel{eq:weighted_metadata}{{10}{3}{}{}{}}
\newlabel{eq:metadata_encoding}{{11}{3}{}{}{}}
\newlabel{eq:attention}{{12}{3}{}{}{}}
\newlabel{eq:concept_fusion}{{13}{3}{}{}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {III-B}2}Lightweight Adaptation Generator}{3}{}\protected@file@percent }
\newlabel{eq:target_layers}{{14}{3}{}{}{}}
\newlabel{eq:adaptation_generation}{{15}{4}{}{}{}}
\newlabel{eq:bottleneck}{{16}{4}{}{}{}}
\newlabel{eq:adapted_computation}{{17}{4}{}{}{}}
\newlabel{eq:parameter_complexity}{{18}{4}{}{}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {III-B}3}Enhanced Drift Memory Module}{4}{}\protected@file@percent }
\newlabel{eq:concept_ema}{{19}{4}{}{}{}}
\newlabel{eq:drift_vector}{{20}{4}{}{}{}}
\newlabel{eq:drift_mean}{{21}{4}{}{}{}}
\newlabel{eq:drift_variance}{{22}{4}{}{}{}}
\newlabel{eq:smoothness_loss}{{23}{4}{}{}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {III-B}4}Energy-aware Loss Function}{4}{}\protected@file@percent }
\newlabel{eq:energy_loss}{{24}{4}{}{}{}}
\newlabel{eq:penalty_term}{{25}{4}{}{}{}}
\newlabel{eq:adaptive_threshold}{{26}{4}{}{}{}}
\newlabel{eq:penalty_weight}{{27}{4}{}{}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {III-C}}CLEAR-E Training Algorithm}{4}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {III-D}}Online Adaptation Algorithm}{4}{}\protected@file@percent }
\@writefile{loa}{\contentsline {algorithm}{\numberline {1}{\ignorespaces CLEAR-E Training Algorithm}}{5}{}\protected@file@percent }
\newlabel{alg:clear_e_training}{{1}{5}{}{}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {III-E}}Training Procedure}{5}{}\protected@file@percent }
\newlabel{sec:training_procedure}{{\mbox  {III-E}}{5}{}{}{}}
\newlabel{eq:frozen_update}{{28}{5}{}{}{}}
\@writefile{loa}{\contentsline {algorithm}{\numberline {2}{\ignorespaces CLEAR-E Online Adaptation}}{5}{}\protected@file@percent }
\newlabel{alg:online_adaptation}{{2}{5}{}{}{}}
\@writefile{lof}{\contentsline {figure}{\numberline {2}{\ignorespaces Energy-aware Loss Function Behavior. The proposed loss function applies higher penalties for under-prediction during high-load periods (right of threshold $\tau $) while maintaining standard behavior for over-prediction and normal-load periods. This asymmetry reflects the operational costs in energy systems where supply shortages are more critical than oversupply.}}{5}{}\protected@file@percent }
\newlabel{fig:energy_loss}{{2}{5}{}{}{}}
\newlabel{eq:unfrozen_update}{{29}{5}{}{}{}}
\@writefile{loa}{\contentsline {algorithm}{\numberline {3}{\ignorespaces CLEAR-E Core Adaptation Process}}{6}{}\protected@file@percent }
\newlabel{alg:clear_e_core}{{3}{6}{}{}{}}
\@writefile{lof}{\contentsline {figure}{\numberline {3}{\ignorespaces CLEAR-E Algorithm Flowchart. The process begins with data reception, performs energy-specific concept encoding, computes drift vectors, updates memory buffer, detects significant drift events, adapts regularization accordingly, generates lightweight adaptations, and applies energy-aware loss for parameter updates. The cycle repeats for continuous online learning.}}{6}{}\protected@file@percent }
\newlabel{fig:algorithm_flow}{{3}{6}{}{}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {III-F}}Computational Efficiency Analysis}{6}{}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{\numberline {IV}Experimental Evaluation}{6}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {IV-A}}Experimental Setup}{6}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {IV-B}}Results and Analysis}{6}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {IV-C}}Main Performance Results}{6}{}\protected@file@percent }
\@writefile{lot}{\contentsline {table}{\numberline {I}{\ignorespaces Forecasting Performance Comparison (24-hour horizon)}}{7}{}\protected@file@percent }
\newlabel{tab:main_results}{{I}{7}{}{}{}}
\@writefile{lot}{\contentsline {table}{\numberline {II}{\ignorespaces Smart Grid-Specific Performance Metrics}}{7}{}\protected@file@percent }
\newlabel{tab:grid_metrics}{{II}{7}{}{}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {IV-D}}Concept Drift Adaptation Analysis}{7}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {IV-E}}Ablation Studies}{7}{}\protected@file@percent }
\@writefile{lot}{\contentsline {table}{\numberline {III}{\ignorespaces Performance Under Concept Drift Scenarios}}{7}{}\protected@file@percent }
\newlabel{tab:drift_scenarios}{{III}{7}{}{}{}}
\@writefile{lot}{\contentsline {table}{\numberline {IV}{\ignorespaces Ablation Study Results (RMSE ± 95\% CI)}}{7}{}\protected@file@percent }
\newlabel{tab:ablation}{{IV}{7}{}{}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {IV-F}}Computational Efficiency Analysis}{7}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {IV-G}}Interpretability and Deployment Validation}{7}{}\protected@file@percent }
\@writefile{lot}{\contentsline {table}{\numberline {V}{\ignorespaces Hyperparameter Sensitivity Analysis (ECL Dataset)}}{8}{}\protected@file@percent }
\newlabel{tab:sensitivity}{{V}{8}{}{}{}}
\@writefile{lof}{\contentsline {figure}{\numberline {4}{\ignorespaces Performance comparison across datasets showing CLEAR-E's consistent improvements over baseline methods. Error bars represent 95\% confidence intervals from 5 independent runs.}}{8}{}\protected@file@percent }
\newlabel{fig:performance}{{4}{8}{}{}{}}
\@writefile{lot}{\contentsline {table}{\numberline {VI}{\ignorespaces Computational Efficiency Comparison}}{8}{}\protected@file@percent }
\newlabel{tab:efficiency}{{VI}{8}{}{}{}}
\@writefile{lof}{\contentsline {figure}{\numberline {5}{\ignorespaces Concept drift adaptation performance showing CLEAR-E's faster recovery compared to PROCEED across different drift scenarios. Recovery time is measured as hours to return to baseline performance.}}{8}{}\protected@file@percent }
\newlabel{fig:drift_adaptation}{{5}{8}{}{}{}}
\@writefile{toc}{\contentsline {section}{\numberline {V}Conclusion}{8}{}\protected@file@percent }
\bibstyle{IEEEtran}
\bibdata{references}
\@writefile{lot}{\contentsline {table}{\numberline {VII}{\ignorespaces Scalability Performance (1000 customers)}}{9}{}\protected@file@percent }
\newlabel{tab:scalability}{{VII}{9}{}{}{}}
\@writefile{lof}{\contentsline {figure}{\numberline {6}{\ignorespaces Feature importance analysis across datasets showing temperature and temporal patterns as primary drivers of energy consumption. Importance weights are learned automatically through the energy-specific concept encoder.}}{9}{}\protected@file@percent }
\newlabel{fig:feature_importance}{{6}{9}{}{}{}}
\@writefile{lot}{\contentsline {table}{\numberline {VIII}{\ignorespaces Feature Importance Analysis (Mean ± Std)}}{9}{}\protected@file@percent }
\newlabel{tab:feature_importance}{{VIII}{9}{}{}{}}
\@writefile{lot}{\contentsline {table}{\numberline {IX}{\ignorespaces Drift Detection Performance}}{9}{}\protected@file@percent }
\newlabel{tab:drift_detection}{{IX}{9}{}{}{}}
\@writefile{lof}{\contentsline {figure}{\numberline {7}{\ignorespaces Sensitivity analysis showing CLEAR-E's robust performance across hyperparameter ranges. The method maintains stable performance within ±50\% of optimal values, crucial for practical deployment.}}{9}{}\protected@file@percent }
\newlabel{fig:sensitivity}{{7}{9}{}{}{}}
\gdef \@abspage@last{9}
