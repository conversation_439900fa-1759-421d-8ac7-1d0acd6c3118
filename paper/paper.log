This is pdfTeX, Version 3.141592653-2.6-1.40.25 (TeX Live 2023/Debian) (preloaded format=pdflatex 2025.7.7)  9 JUL 2025 14:33
entering extended mode
 restricted \write18 enabled.
 %&-line parsing enabled.
**paper.tex
(./paper.tex
LaTeX2e <2023-11-01> patch level 1
L3 programming layer <2024-01-22>
(/usr/share/texlive/texmf-dist/tex/latex/ieeetran/IEEEtran.cls
Document Class: IEEEtran 2015/08/26 V1.8b by <PERSON>
-- See the "IEEEtran_HOWTO" manual for usage information.
-- http://www.michaelshell.org/tex/ieeetran/
\@IEEEtrantmpdimenA=\dimen140
\@IEEEtrantmpdimenB=\dimen141
\@IEEEtrantmpdimenC=\dimen142
\@IEEEtrantmpcountA=\count187
\@IEEEtrantmpcountB=\count188
\@IEEEtrantmpcountC=\count189
\@IEEEtrantmptoksA=\toks17
LaTeX Font Info:    Trying to load font information for OT1+ptm on input line 5
03.
(/usr/share/texlive/texmf-dist/tex/latex/psnfss/ot1ptm.fd
File: ot1ptm.fd 2001/06/04 font definitions for OT1/ptm.
)
-- Using 8.5in x 11in (letter) paper.
-- Using PDF output.
\@IEEEnormalsizeunitybaselineskip=\dimen143
-- This is a 10 point document.
\CLASSINFOnormalsizebaselineskip=\dimen144
\CLASSINFOnormalsizeunitybaselineskip=\dimen145
\IEEEnormaljot=\dimen146
LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <5> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <5> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1090.

LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <7> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <7> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1090.

LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <8> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <8> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1090.

LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <9> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <9> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1090.

LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <10> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <10> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1090.

LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <11> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <11> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1090.

LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <12> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <12> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1090.

LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <17> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <17> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1090.

LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <20> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <20> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1090.

LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <24> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <24> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1090.

\IEEEquantizedlength=\dimen147
\IEEEquantizedlengthdiff=\dimen148
\IEEEquantizedtextheightdiff=\dimen149
\IEEEilabelindentA=\dimen150
\IEEEilabelindentB=\dimen151
\IEEEilabelindent=\dimen152
\IEEEelabelindent=\dimen153
\IEEEdlabelindent=\dimen154
\IEEElabelindent=\dimen155
\IEEEiednormlabelsep=\dimen156
\IEEEiedmathlabelsep=\dimen157
\IEEEiedtopsep=\skip48
\c@section=\count190
\c@subsection=\count191
\c@subsubsection=\count192
\c@paragraph=\count193
\c@IEEEsubequation=\count194
\abovecaptionskip=\skip49
\belowcaptionskip=\skip50
\c@figure=\count195
\c@table=\count196
\@IEEEeqnnumcols=\count197
\@IEEEeqncolcnt=\count198
\@IEEEsubeqnnumrollback=\count199
\@IEEEquantizeheightA=\dimen158
\@IEEEquantizeheightB=\dimen159
\@IEEEquantizeheightC=\dimen160
\@IEEEquantizeprevdepth=\dimen161
\@IEEEquantizemultiple=\count266
\@IEEEquantizeboxA=\box51
\@IEEEtmpitemindent=\dimen162
\IEEEPARstartletwidth=\dimen163
\c@IEEEbiography=\count267
\@IEEEtranrubishbin=\box52
) (/usr/share/texlive/texmf-dist/tex/latex/amsmath/amsmath.sty
Package: amsmath 2023/05/13 v2.17o AMS math features
\@mathmargin=\skip51

For additional information on amsmath, use the `?' option.
(/usr/share/texlive/texmf-dist/tex/latex/amsmath/amstext.sty
Package: amstext 2021/08/26 v2.01 AMS text

(/usr/share/texlive/texmf-dist/tex/latex/amsmath/amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks18
\ex@=\dimen164
))
(/usr/share/texlive/texmf-dist/tex/latex/amsmath/amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen165
)
(/usr/share/texlive/texmf-dist/tex/latex/amsmath/amsopn.sty
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count268
LaTeX Info: Redefining \frac on input line 234.
\uproot@=\count269
\leftroot@=\count270
LaTeX Info: Redefining \overline on input line 399.
LaTeX Info: Redefining \colon on input line 410.
\classnum@=\count271
\DOTSCASE@=\count272
LaTeX Info: Redefining \ldots on input line 496.
LaTeX Info: Redefining \dots on input line 499.
LaTeX Info: Redefining \cdots on input line 620.
\Mathstrutbox@=\box53
\strutbox@=\box54
LaTeX Info: Redefining \big on input line 722.
LaTeX Info: Redefining \Big on input line 723.
LaTeX Info: Redefining \bigg on input line 724.
LaTeX Info: Redefining \Bigg on input line 725.
\big@size=\dimen166
LaTeX Font Info:    Redeclaring font encoding OML on input line 743.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 744.
\macc@depth=\count273
LaTeX Info: Redefining \bmod on input line 905.
LaTeX Info: Redefining \pmod on input line 910.
LaTeX Info: Redefining \smash on input line 940.
LaTeX Info: Redefining \relbar on input line 970.
LaTeX Info: Redefining \Relbar on input line 971.
\c@MaxMatrixCols=\count274
\dotsspace@=\muskip16
\c@parentequation=\count275
\dspbrk@lvl=\count276
\tag@help=\toks19
\row@=\count277
\column@=\count278
\maxfields@=\count279
\andhelp@=\toks20
\eqnshift@=\dimen167
\alignsep@=\dimen168
\tagshift@=\dimen169
\tagwidth@=\dimen170
\totwidth@=\dimen171
\lineht@=\dimen172
\@envbody=\toks21
\multlinegap=\skip52
\multlinetaggap=\skip53
\mathdisplay@stack=\toks22
LaTeX Info: Redefining \[ on input line 2953.
LaTeX Info: Redefining \] on input line 2954.
)
(/usr/share/texlive/texmf-dist/tex/latex/amsfonts/amsfonts.sty
Package: amsfonts 2013/01/14 v3.01 Basic AMSFonts support
\symAMSa=\mathgroup4
\symAMSb=\mathgroup5
LaTeX Font Info:    Redeclaring math symbol \hbar on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathfrak' in version `bold'
(Font)                  U/euf/m/n --> U/euf/b/n on input line 106.
)
(/usr/share/texlive/texmf-dist/tex/latex/amscls/amsthm.sty
Package: amsthm 2020/05/29 v2.20.6
\thm@style=\toks23
\thm@bodyfont=\toks24
\thm@headfont=\toks25
\thm@notefont=\toks26
\thm@headpunct=\toks27
\thm@preskip=\skip54
\thm@postskip=\skip55
\thm@headsep=\skip56
\dth@everypar=\toks28
)
(/usr/share/texlive/texmf-dist/tex/latex/algorithms/algorithmic.sty
Package: algorithmic 2009/08/24 v0.1 Document Style `algorithmic'

(/usr/share/texlive/texmf-dist/tex/latex/base/ifthen.sty
Package: ifthen 2022/04/13 v1.1d Standard LaTeX ifthen package (DPC)
)
(/usr/share/texlive/texmf-dist/tex/latex/graphics/keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks29
)
\c@ALC@unique=\count280
\c@ALC@line=\count281
\c@ALC@rem=\count282
\c@ALC@depth=\count283
\ALC@tlm=\skip57
\algorithmicindent=\skip58
)
(/usr/share/texlive/texmf-dist/tex/latex/algorithms/algorithm.sty
Package: algorithm 2009/08/24 v0.1 Document Style `algorithm' - floating enviro
nment

(/usr/share/texlive/texmf-dist/tex/latex/float/float.sty
Package: float 2001/11/08 v1.3d Float enhancements (AL)
\c@float@type=\count284
\float@exts=\toks30
\float@box=\box55
\@float@everytoks=\toks31
\@floatcapt=\box56
)
\@float@every@algorithm=\toks32
\c@algorithm=\count285
)
(/usr/share/texlive/texmf-dist/tex/latex/tools/array.sty
Package: array 2023/10/16 v2.5g Tabular extension package (FMi)
\col@sep=\dimen173
\ar@mcellbox=\box57
\extrarowheight=\dimen174
\NC@list=\toks33
\extratabsurround=\skip59
\backup@length=\skip60
\ar@cellbox=\box58
)
(/usr/share/texlive/texmf-dist/tex/latex/subfig/subfig.sty
Package: subfig 2005/06/28 ver: 1.3 subfig package

(/usr/share/texlive/texmf-dist/tex/latex/caption/caption3.sty
Package: caption3 2023/07/31 v2.4d caption3 kernel (AR)
\caption@tempdima=\dimen175
\captionmargin=\dimen176
\caption@leftmargin=\dimen177
\caption@rightmargin=\dimen178
\caption@width=\dimen179
\caption@indent=\dimen180
\caption@parindent=\dimen181
\caption@hangindent=\dimen182
Package caption Info: Unknown document class (or package),
(caption)             standard defaults will be used.
Package caption Info: \@makecaption = \long macro:#1#2->\ifx \@captype \@IEEEta
blestring \footnotesize \bgroup \par \centering \@IEEEtabletopskipstrut {\norma
lfont \footnotesize #1}\\{\normalfont \footnotesize \scshape #2}\par \addvspace
 {0.5\baselineskip }\egroup \@IEEEtablecaptionsepspace \else \@IEEEfigurecaptio
nsepspace \setbox \@tempboxa \hbox {\normalfont \footnotesize {#1.}\nobreakspac
e \nobreakspace #2}\ifdim \wd \@tempboxa >\hsize \setbox \@tempboxa \hbox {\nor
malfont \footnotesize {#1.}\nobreakspace \nobreakspace }\parbox [t]{\hsize }{\n
ormalfont \footnotesize \noindent \unhbox \@tempboxa #2}\else \ifCLASSOPTIONcon
ference \hbox to\hsize {\normalfont \footnotesize \hfil \box \@tempboxa \hfil }
\else \hbox to\hsize {\normalfont \footnotesize \box \@tempboxa \hfil }\fi \fi 
\fi  on input line 1175.
)
\c@KVtest=\count286
\sf@farskip=\skip61
\sf@captopadj=\dimen183
\sf@capskip=\skip62
\sf@nearskip=\skip63
\c@subfigure=\count287
\c@subfigure@save=\count288
\c@lofdepth=\count289
\c@subtable=\count290
\c@subtable@save=\count291
\c@lotdepth=\count292
\sf@top=\skip64
\sf@bottom=\skip65
)
(/usr/share/texlive/texmf-dist/tex/latex/base/textcomp.sty
Package: textcomp 2020/02/02 v2.0n Standard LaTeX package
)
(/usr/share/texlive/texmf-dist/tex/latex/sttools/stfloats.sty
Package: stfloats 2017/03/27 v3.3 Improve float mechanism and baselineskip sett
ings
\@dblbotnum=\count293
\c@dblbotnumber=\count294
)
(/usr/share/texlive/texmf-dist/tex/latex/url/url.sty
\Urlmuskip=\muskip17
Package: url 2013/09/16  ver 3.4  Verb mode for urls, etc.
)
(/usr/share/texlive/texmf-dist/tex/latex/tools/verbatim.sty
Package: verbatim 2023-11-06 v1.5v LaTeX2e package for verbatim enhancements
\every@verbatim=\toks34
\verbatim@line=\toks35
\verbatim@in@stream=\read2
)
(/usr/share/texlive/texmf-dist/tex/latex/graphics/graphicx.sty
Package: graphicx 2021/09/16 v1.2d Enhanced LaTeX Graphics (DPC,SPQR)

(/usr/share/texlive/texmf-dist/tex/latex/graphics/graphics.sty
Package: graphics 2022/03/10 v1.4e Standard LaTeX Graphics (DPC,SPQR)

(/usr/share/texlive/texmf-dist/tex/latex/graphics/trig.sty
Package: trig 2021/08/11 v1.11 sin cos tan (DPC)
)
(/usr/share/texlive/texmf-dist/tex/latex/graphics-cfg/graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: pdftex.def on input line 107.

(/usr/share/texlive/texmf-dist/tex/latex/graphics-def/pdftex.def
File: pdftex.def 2022/09/22 v1.2b Graphics/color driver for pdftex
))
\Gin@req@height=\dimen184
\Gin@req@width=\dimen185
)
(/usr/share/texlive/texmf-dist/tex/latex/cite/cite.sty
LaTeX Info: Redefining \cite on input line 302.
LaTeX Info: Redefining \nocite on input line 332.
Package: cite 2015/02/27  v 5.5
)
(/usr/share/texlive/texmf-dist/tex/latex/booktabs/booktabs.sty
Package: booktabs 2020/01/12 v1.61803398 Publication quality tables
\heavyrulewidth=\dimen186
\lightrulewidth=\dimen187
\cmidrulewidth=\dimen188
\belowrulesep=\dimen189
\belowbottomsep=\dimen190
\aboverulesep=\dimen191
\abovetopsep=\dimen192
\cmidrulesep=\dimen193
\cmidrulekern=\dimen194
\defaultaddspace=\dimen195
\@cmidla=\count295
\@cmidlb=\count296
\@aboverulesep=\dimen196
\@belowrulesep=\dimen197
\@thisruleclass=\count297
\@lastruleclass=\count298
\@thisrulewidth=\dimen198
)
(/usr/share/texlive/texmf-dist/tex/latex/multirow/multirow.sty
Package: multirow 2021/03/15 v2.8 Span multiple rows of a table
\multirow@colwidth=\skip66
\multirow@cntb=\count299
\multirow@dima=\skip67
\bigstrutjot=\dimen199
)
(/usr/share/texlive/texmf-dist/tex/latex/xcolor/xcolor.sty
Package: xcolor 2023/11/15 v3.01 LaTeX color extensions (UK)

(/usr/share/texlive/texmf-dist/tex/latex/graphics-cfg/color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package xcolor Info: Driver file: pdftex.def on input line 274.

(/usr/share/texlive/texmf-dist/tex/latex/graphics/mathcolor.ltx)
Package xcolor Info: Model `cmy' substituted by `cmy0' on input line 1350.
Package xcolor Info: Model `hsb' substituted by `rgb' on input line 1354.
Package xcolor Info: Model `RGB' extended on input line 1366.
Package xcolor Info: Model `HTML' substituted by `rgb' on input line 1368.
Package xcolor Info: Model `Hsb' substituted by `hsb' on input line 1369.
Package xcolor Info: Model `tHsb' substituted by `hsb' on input line 1370.
Package xcolor Info: Model `HSB' substituted by `hsb' on input line 1371.
Package xcolor Info: Model `Gray' substituted by `gray' on input line 1372.
Package xcolor Info: Model `wave' substituted by `hsb' on input line 1373.
)
\c@theorem=\count300
\c@lemma=\count301
\c@proposition=\count302
\c@corollary=\count303
\c@assumption=\count304

(/usr/share/texlive/texmf-dist/tex/latex/l3backend/l3backend-pdftex.def
File: l3backend-pdftex.def 2024-01-04 L3 backend support: PDF output (pdfTeX)
\l__color_backend_stack_int=\count305
\l__pdf_internal_box=\box59
)
(./paper.aux)
\openout1 = `paper.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 26.
LaTeX Font Info:    ... okay on input line 26.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 26.
LaTeX Font Info:    ... okay on input line 26.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 26.
LaTeX Font Info:    ... okay on input line 26.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 26.
LaTeX Font Info:    ... okay on input line 26.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 26.
LaTeX Font Info:    ... okay on input line 26.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 26.
LaTeX Font Info:    ... okay on input line 26.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 26.
LaTeX Font Info:    ... okay on input line 26.

-- Lines per column: 58 (exact).
Package caption Info: Begin \AtBeginDocument code.
Package caption Info: subfig package v1.3 is loaded.
Package caption Info: End \AtBeginDocument code.
(/usr/share/texlive/texmf-dist/tex/context/base/mkii/supp-pdf.mkii
[Loading MPS to PDF converter (version 2006.09.02).]
\scratchcounter=\count306
\scratchdimen=\dimen256
\scratchbox=\box60
\nofMPsegments=\count307
\nofMParguments=\count308
\everyMPshowfont=\toks36
\MPscratchCnt=\count309
\MPscratchDim=\dimen257
\MPnumerator=\count310
\makeMPintoPDFobject=\count311
\everyMPtoPDFconversion=\toks37
) (/usr/share/texlive/texmf-dist/tex/latex/epstopdf-pkg/epstopdf-base.sty
Package: epstopdf-base 2020-01-24 v2.11 Base part for package epstopdf
Package epstopdf-base Info: Redefining graphics rule for `.eps' on input line 4
85.

(/usr/share/texlive/texmf-dist/tex/latex/latexconfig/epstopdf-sys.cfg
File: epstopdf-sys.cfg 2010/07/13 v1.3 Configuration of (r)epstopdf for TeX Liv
e
))

LaTeX Warning: Citation `hong2016probabilistic' on page 1 undefined on input li
ne 48.


LaTeX Warning: Citation `wang2018review' on page 1 undefined on input line 48.


LaTeX Warning: Citation `taylor2003short' on page 1 undefined on input line 50.



LaTeX Warning: Citation `haben2021review' on page 1 undefined on input line 50.



LaTeX Warning: Citation `torres2021deep' on page 1 undefined on input line 52.


LaTeX Warning: Citation `zhou2021informer' on page 1 undefined on input line 52
.


LaTeX Warning: Citation `houlsby2019parameter' on page 1 undefined on input lin
e 54.


LaTeX Warning: Citation `zhao2025proceed' on page 1 undefined on input line 54.



Underfull \hbox (badness 7814) in paragraph at lines 54--55
[]\OT1/ptm/m/n/10 Parameter-efficient fine-tuning (PEFT) meth-ods have
 []


Underfull \hbox (badness 2080) in paragraph at lines 56--57
[]\OT1/ptm/m/n/10 In this pa-per, we pro-pose CLEAR-E (Concept-aware
 []

LaTeX Font Info:    Trying to load font information for U+msa on input line 59.

(/usr/share/texlive/texmf-dist/tex/latex/amsfonts/umsa.fd
File: umsa.fd 2013/01/14 v3.01 AMS symbols A
)
LaTeX Font Info:    Trying to load font information for U+msb on input line 59.


(/usr/share/texlive/texmf-dist/tex/latex/amsfonts/umsb.fd
File: umsb.fd 2013/01/14 v3.01 AMS symbols B
)

LaTeX Warning: Citation `hippert2001neural' on page 1 undefined on input line 7
3.


LaTeX Warning: Citation `contreras2003arima' on page 1 undefined on input line 
73.


LaTeX Warning: Citation `taylor2003short' on page 1 undefined on input line 73.



LaTeX Warning: Citation `chen2004load' on page 1 undefined on input line 73.


Underfull \vbox (badness 10000) has occurred while \output is active []

 [1{/var/lib/texmf/fonts/map/pdftex/updmap/pdftex.map}{/usr/share/texlive/texmf
-dist/fonts/enc/dvips/base/8r.enc}


]

LaTeX Warning: Citation `torres2021deep' on page 2 undefined on input line 75.


LaTeX Warning: Citation `shi2018deep' on page 2 undefined on input line 75.


LaTeX Warning: Citation `kong2017short' on page 2 undefined on input line 75.


LaTeX Warning: Citation `zhou2021informer' on page 2 undefined on input line 75
.


LaTeX Warning: Citation `wu2021autoformer' on page 2 undefined on input line 75
.


LaTeX Warning: Citation `gama2014survey' on page 2 undefined on input line 80.


LaTeX Warning: Citation `haben2021review' on page 2 undefined on input line 80.



LaTeX Warning: Citation `kuncheva2004classifier' on page 2 undefined on input l
ine 82.


LaTeX Warning: Citation `losing2018incremental' on page 2 undefined on input li
ne 82.


LaTeX Warning: Citation `gama2004learning' on page 2 undefined on input line 82
.


LaTeX Warning: Citation `finn2017model' on page 2 undefined on input line 84.


LaTeX Warning: Citation `kirkpatrick2017overcoming' on page 2 undefined on inpu
t line 84.


LaTeX Warning: Citation `houlsby2019parameter' on page 2 undefined on input lin
e 87.


LaTeX Warning: Citation `houlsby2019parameter' on page 2 undefined on input lin
e 87.


LaTeX Warning: Citation `hu2021lora' on page 2 undefined on input line 87.


LaTeX Warning: Citation `lester2021power' on page 2 undefined on input line 87.



LaTeX Warning: Citation `zhao2025proceed' on page 2 undefined on input line 89.


[2]
Overfull \hbox (21.14575pt too wide) in paragraph at lines 231--232
 [][] 
 []

LaTeX Font Info:    Trying to load font information for OMS+ptm on input line 2
84.
(/usr/share/texlive/texmf-dist/tex/latex/psnfss/omsptm.fd
File: omsptm.fd 
)
LaTeX Font Info:    Font shape `OMS/ptm/m/n' in size <10> not available
(Font)              Font shape `OMS/cmsy/m/n' tried instead on input line 284.

Underfull \hbox (badness 3029) in paragraph at lines 319--320
 \OT1/ptm/m/it/10 2) Lightweight Adap-ta-tion Gen-er-a-tor: [][] \OT1/ptm/m/n/1
0 The adap-ta-tion
 []

[3] [4]
Underfull \vbox (badness 7667) has occurred while \output is active []

 [5]
Underfull \hbox (badness 10000) in paragraph at lines 630--631
[] []\OT1/ptm/m/n/10 Normalize: $\OT1/cmr/bx/n/10 X[] \OMS/cmsy/m/n/10   []\OT1
/cmr/m/n/10 (\OT1/cmr/bx/n/10 X[]\OT1/cmr/m/n/10 )$\OT1/ptm/m/n/10 , $\OT1/cmr/
bx/n/10 M[] \OMS/cmsy/m/n/10  
 []


Underfull \hbox (badness 10000) in paragraph at lines 641--642
[] []\OT1/ptm/m/n/10 Set reg-u-lar-iza-tion: $\OML/cmm/m/it/10 ^^U[] \OT1/cmr/m
/n/10 =
 []


Underfull \hbox (badness 2875) in paragraph at lines 644--645
[] []\OT1/ptm/m/n/10 Generate adap-ta-tions: $\OMS/cmsy/m/n/10 f[][]g[] \OT1/cm
r/m/n/10 = \OMS/cmsy/m/n/10 A[]\OT1/cmr/m/n/10 (\OT1/cmr/bx/n/10 d[]\OT1/cmr/m/
n/10 )$ \OT1/ptm/m/it/10 //
 []


Underfull \hbox (badness 10000) in paragraph at lines 645--646
[] []\OT1/ptm/m/n/10 Apply adap-ta-tions: $\OML/cmm/m/it/10 f[] \OT1/cmr/m/n/10
 =
 []


Underfull \hbox (badness 10000) in paragraph at lines 650--651
[] []\OT1/ptm/m/n/10 Compute en-ergy loss: $\OMS/cmsy/m/n/10 L[] \OT1/cmr/m/n/1
0 = \OMS/cmsy/m/n/10 L[] \OT1/cmr/m/n/10 +
 []


Underfull \hbox (badness 10000) in paragraph at lines 651--652
[] []\OT1/ptm/m/n/10 Compute smooth-ness loss: $\OMS/cmsy/m/n/10 L[] \OT1/cmr/m
/n/10 =
 []


Overfull \hbox (26.83575pt too wide) in paragraph at lines 757--758
 [][] 
 []

[6]
Underfull \hbox (badness 1629) in paragraph at lines 811--812
[]\OT1/ptm/b/n/10 Theorem 4 \OT1/ptm/m/n/10 (Bounded Adap-ta-tion Vari-ance)\OT
1/ptm/b/n/10 . []\OT1/ptm/m/it/10 Un-der the
 []

[7]

LaTeX Warning: Citation `zhao2025proceed' on page 8 undefined on input line 891
.


LaTeX Warning: Citation `nie2022time' on page 8 undefined on input line 892.


LaTeX Warning: Citation `zeng2023transformers' on page 8 undefined on input lin
e 893.


LaTeX Warning: Citation `bai2018empirical' on page 8 undefined on input line 89
4.


LaTeX Warning: Citation `liu2023itransformer' on page 8 undefined on input line
 895.


Underfull \hbox (badness 10000) in paragraph at lines 903--904
[]\OT1/ptm/m/n/10 Mean Ab-so-lute Per-cent-age Er-ror (MAPE):
 []


LaTeX Warning: Reference `fig:efficiency' on page 8 undefined on input line 982
.


LaTeX Warning: Reference `fig:interpretability' on page 8 undefined on input li
ne 993.

[8]

LaTeX Warning: Reference `fig:memory_size' on page 9 undefined on input line 10
45.


Underfull \hbox (badness 4403) in paragraph at lines 1133--1134
[]\OT1/ptm/m/n/10 Extensive ex-per-i-ments on real-world en-ergy datasets
 []

[9]
No file paper.bbl.
[10] (./paper.aux)
 ***********
LaTeX2e <2023-11-01> patch level 1
L3 programming layer <2024-01-22>
 ***********


LaTeX Warning: There were undefined references.

 ) 
Here is how much of TeX's memory you used:
 5555 strings out of 476106
 93407 string characters out of 5793934
 2056975 words of memory out of 5000000
 27466 multiletter control sequences out of 15000+600000
 608897 words of font info for 132 fonts, out of 8000000 for 9000
 63 hyphenation exceptions out of 8191
 57i,15n,65p,1421b,491s stack positions out of 10000i,1000n,20000p,200000b,200000s
</usr/share/texlive/texmf-dist/fonts/type1/public/amsfonts/cm/cmbx10.pfb></us
r/share/texlive/texmf-dist/fonts/type1/public/amsfonts/cm/cmbx7.pfb></usr/share
/texlive/texmf-dist/fonts/type1/public/amsfonts/cm/cmbx8.pfb></usr/share/texliv
e/texmf-dist/fonts/type1/public/amsfonts/cm/cmex10.pfb></usr/share/texlive/texm
f-dist/fonts/type1/public/amsfonts/cm/cmmi10.pfb></usr/share/texlive/texmf-dist
/fonts/type1/public/amsfonts/cm/cmmi5.pfb></usr/share/texlive/texmf-dist/fonts/
type1/public/amsfonts/cm/cmmi6.pfb></usr/share/texlive/texmf-dist/fonts/type1/p
ublic/amsfonts/cm/cmmi7.pfb></usr/share/texlive/texmf-dist/fonts/type1/public/a
msfonts/cm/cmmi8.pfb></usr/share/texlive/texmf-dist/fonts/type1/public/amsfonts
/cm/cmmib10.pfb></usr/share/texlive/texmf-dist/fonts/type1/public/amsfonts/cmex
tra/cmmib8.pfb></usr/share/texlive/texmf-dist/fonts/type1/public/amsfonts/cm/cm
r10.pfb></usr/share/texlive/texmf-dist/fonts/type1/public/amsfonts/cm/cmr5.pfb>
</usr/share/texlive/texmf-dist/fonts/type1/public/amsfonts/cm/cmr7.pfb></usr/sh
are/texlive/texmf-dist/fonts/type1/public/amsfonts/cm/cmsy10.pfb></usr/share/te
xlive/texmf-dist/fonts/type1/public/amsfonts/cm/cmsy5.pfb></usr/share/texlive/t
exmf-dist/fonts/type1/public/amsfonts/cm/cmsy7.pfb></usr/share/texlive/texmf-di
st/fonts/type1/public/amsfonts/cm/cmsy8.pfb></usr/share/texlive/texmf-dist/font
s/type1/public/amsfonts/latxfont/lcircle1.pfb></usr/share/texlive/texmf-dist/fo
nts/type1/public/amsfonts/latxfont/line10.pfb></usr/share/texlive/texmf-dist/fo
nts/type1/public/amsfonts/symbols/msbm10.pfb></usr/share/texlive/texmf-dist/fon
ts/type1/urw/times/utmb8a.pfb></usr/share/texlive/texmf-dist/fonts/type1/urw/ti
mes/utmbi8a.pfb></usr/share/texlive/texmf-dist/fonts/type1/urw/times/utmr8a.pfb
></usr/share/texlive/texmf-dist/fonts/type1/urw/times/utmri8a.pfb>
Output written on paper.pdf (10 pages, 344429 bytes).
PDF statistics:
 163 PDF objects out of 1000 (max. 8388607)
 100 compressed objects within 1 object stream
 0 named destinations out of 1000 (max. 500000)
 1 words of extra memory for PDF output out of 10000 (max. 10000000)

