% Generated by IEEEtran.bst, version: 1.14 (2015/08/26)
\begin{thebibliography}{10}
\providecommand{\url}[1]{#1}
\csname url@samestyle\endcsname
\providecommand{\newblock}{\relax}
\providecommand{\bibinfo}[2]{#2}
\providecommand{\BIBentrySTDinterwordspacing}{\spaceskip=0pt\relax}
\providecommand{\BIBentryALTinterwordstretchfactor}{4}
\providecommand{\BIBentryALTinterwordspacing}{\spaceskip=\fontdimen2\font plus
\BIBentryALTinterwordstretchfactor\fontdimen3\font minus
  \fontdimen4\font\relax}
\providecommand{\BIBforeignlanguage}[2]{{%
\expandafter\ifx\csname l@#1\endcsname\relax
\typeout{** WARNING: IEEEtran.bst: No hyphenation pattern has been}%
\typeout{** loaded for the language `#1'. Using the pattern for}%
\typeout{** the default language instead.}%
\else
\language=\csname l@#1\endcsname
\fi
#2}}
\providecommand{\BIBdecl}{\relax}
\BIBdecl

\bibitem{hong2016probabilistic}
T.~Hong, P.~<PERSON>nson, S.~Fan, H.~Zareipour, A.~Troccoli, and R.~J. Hyndman,
  ``Probabilistic energy forecasting: Global energy forecasting competition
  2014 and beyond,'' \emph{International Journal of Forecasting}, vol.~32,
  no.~3, pp. 896--913, 2016.

\bibitem{wang2018review}
H.~Wang, Z.~Lei, X.~Zhang, B.~Zhou, and J.~Peng, ``A review of deep learning
  for renewable energy forecasting,'' \emph{Energy Conversion and Management},
  vol. 198, p. 111799, 2019.

\bibitem{taylor2003short}
J.~W. Taylor, ``Short-term electricity demand forecasting using double seasonal
  exponential smoothing,'' \emph{Journal of the Operational Research Society},
  vol.~54, no.~8, pp. 799--805, 2003.

\bibitem{haben2021review}
S.~Haben, M.~Rowe, D.~V. Greetham, P.~Grindrod, W.~Holderbaum, B.~Potter, and
  C.~Singleton, ``Review of low voltage load forecasting: Methods,
  applications, and recommendations,'' \emph{Applied Energy}, vol. 304, p.
  117798, 2021.

\bibitem{torres2021deep}
J.~F. Torres, D.~Hadjout, A.~Sebaa, F.~Mart{\'\i}nez-{\'A}lvarez, and
  A.~Troncoso, ``Deep learning for time series forecasting: a survey,''
  \emph{Big Data}, vol.~9, no.~1, pp. 3--21, 2021.

\bibitem{zhou2021informer}
H.~Zhou, S.~Zhang, J.~Peng, S.~Zhang, J.~Li, H.~Xiong, and W.~Zhang,
  ``Informer: Beyond efficient transformer for long sequence time-series
  forecasting,'' in \emph{Proceedings of the AAAI Conference on Artificial
  Intelligence}, vol.~35, no.~12, 2021, pp. 11\,106--11\,115.

\bibitem{houlsby2019parameter}
N.~Houlsby, A.~Giurgiu, S.~Jastrzebski, B.~Morrone, Q.~De~Laroussilhe,
  A.~Gesmundo, M.~Attariyan, and S.~Gelly, ``Parameter-efficient transfer
  learning for nlp,'' in \emph{International Conference on Machine
  Learning}.\hskip 1em plus 0.5em minus 0.4em\relax PMLR, 2019, pp. 2790--2799.

\bibitem{zhao2025proceed}
L.~Zhao and Y.~Shen, ``Proactive model adaptation against concept drift for
  online time series forecasting,'' \emph{Proceedings of the 31st ACM SIGKDD
  Conference on Knowledge Discovery and Data Mining}, 2025.

\bibitem{hippert2001neural}
H.~S. Hippert, C.~E. Pedreira, and R.~C. Souza, ``Neural networks for
  short-term load forecasting: a review and evaluation,'' \emph{IEEE
  Transactions on power systems}, vol.~16, no.~1, pp. 44--55, 2001.

\bibitem{contreras2003arima}
J.~Contreras, R.~Espinola, F.~J. Nogales, and A.~J. Conejo, ``Arima models to
  predict next-day electricity prices,'' \emph{IEEE transactions on power
  systems}, vol.~18, no.~3, pp. 1014--1020, 2003.

\bibitem{chen2004load}
B.-J. Chen, M.-W. Chang, and C.-J. Lin, ``Load forecasting using support vector
  machines: a study on eunite competition 2001,'' \emph{IEEE transactions on
  power systems}, vol.~19, no.~4, pp. 1821--1830, 2004.

\bibitem{shi2018deep}
H.~Shi, M.~Xu, and R.~Li, ``Deep learning for household load forecasting—a
  novel pooling deep rnn,'' \emph{IEEE Transactions on Smart Grid}, vol.~9,
  no.~5, pp. 5271--5280, 2017.

\bibitem{kong2017short}
W.~Kong, Z.~Y. Dong, Y.~Jia, D.~J. Hill, Y.~Xu, and Y.~Zhang, ``Short-term
  residential load forecasting based on lstm recurrent neural network,''
  \emph{IEEE Transactions on Smart Grid}, vol.~10, no.~1, pp. 841--851, 2017.

\bibitem{wu2021autoformer}
H.~Wu, J.~Xu, J.~Wang, and M.~Long, ``Autoformer: Decomposition transformers
  with auto-correlation for long-term series forecasting,'' in \emph{Advances
  in Neural Information Processing Systems}, vol.~34, 2021, pp.
  22\,419--22\,430.

\bibitem{gama2014survey}
J.~Gama, I.~{\v{Z}}liobait{\.e}, A.~Bifet, M.~Pechenizkiy, and A.~Bouchachia,
  ``A survey on concept drift adaptation,'' \emph{ACM computing surveys},
  vol.~46, no.~4, pp. 1--37, 2014.

\bibitem{kuncheva2004classifier}
L.~I. Kuncheva, ``Classifier ensembles for changing environments,'' in
  \emph{International workshop on multiple classifier systems}.\hskip 1em plus
  0.5em minus 0.4em\relax Springer, 2004, pp. 1--15.

\bibitem{losing2018incremental}
V.~Losing, B.~Hammer, and H.~Wersing, ``Incremental on-line learning: A review
  and comparison of state of the art algorithms,'' \emph{Neurocomputing}, vol.
  275, pp. 1261--1274, 2018.

\bibitem{gama2004learning}
J.~Gama, P.~Medas, G.~Castillo, and P.~Rodrigues, ``Learning with drift
  detection,'' in \emph{Brazilian symposium on artificial intelligence}.\hskip
  1em plus 0.5em minus 0.4em\relax Springer, 2004, pp. 286--295.

\bibitem{finn2017model}
C.~Finn, P.~Abbeel, and S.~Levine, ``Model-agnostic meta-learning for fast
  adaptation of deep networks,'' in \emph{International conference on machine
  learning}.\hskip 1em plus 0.5em minus 0.4em\relax PMLR, 2017, pp. 1126--1135.

\bibitem{kirkpatrick2017overcoming}
J.~Kirkpatrick, R.~Pascanu, N.~Rabinowitz, J.~Veness, G.~Desjardins, A.~A.
  Rusu, K.~Milan, J.~Quan, T.~Ramalho, A.~Grabska-Barwinska \emph{et~al.},
  ``Overcoming catastrophic forgetting in neural networks,'' \emph{Proceedings
  of the national academy of sciences}, vol. 114, no.~13, pp. 3521--3526, 2017.

\bibitem{hu2021lora}
E.~J. Hu, Y.~Shen, P.~Wallis, Z.~Allen-Zhu, Y.~Li, S.~Wang, L.~Wang, and
  W.~Chen, ``Lora: Low-rank adaptation of large language models,'' in
  \emph{International Conference on Learning Representations}, 2022.

\bibitem{lester2021power}
B.~Lester, R.~Al-Rfou, and N.~Constant, ``The power of scale for
  parameter-efficient prompt tuning,'' in \emph{Proceedings of the 2021
  Conference on Empirical Methods in Natural Language Processing}, 2021, pp.
  3045--3059.

\bibitem{nie2022time}
Y.~Nie, N.~H. Nguyen, P.~Sinthong, and J.~Kalagnanam, ``A time series is worth
  64 words: Long-term forecasting with transformers,'' in \emph{International
  Conference on Learning Representations}, 2023.

\bibitem{zeng2023transformers}
A.~Zeng, M.~Chen, L.~Zhang, and Q.~Xu, ``Are transformers effective for time
  series forecasting?'' in \emph{Proceedings of the AAAI conference on
  artificial intelligence}, vol.~37, no.~9, 2023, pp. 11\,121--11\,128.

\bibitem{bai2018empirical}
S.~Bai, J.~Z. Kolter, and V.~Koltun, ``An empirical evaluation of generic
  convolutional and recurrent networks for sequence modeling,'' in \emph{arXiv
  preprint arXiv:1803.01271}, 2018.

\bibitem{liu2023itransformer}
Y.~Liu, H.~Wu, J.~Wang, and M.~Long, ``itransformer: Inverted transformers are
  effective for time series forecasting,'' in \emph{International Conference on
  Learning Representations}, 2024.

\end{thebibliography}
