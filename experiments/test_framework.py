#!/usr/bin/env python3
"""
Test Script for CLEAR-E Experimental Framework
Validates the experimental setup and ensures all components work correctly
"""

import os
import sys
import unittest
import numpy as np
import pandas as pd
import torch
import tempfile
import shutil

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from experimental_framework import ExperimentalFramework, SmartGridDataset, SmartGridMetrics, ConceptDriftSimulator
from clear_e_model import CLEAR_E, EnergySpecificConceptEncoder, LightweightAdaptationGenerator
from baseline_models import create_baseline_model, LSTMModel, TransformerModel

class TestSmartGridDataset(unittest.TestCase):
    """Test SmartGridDataset functionality"""
    
    def setUp(self):
        """Create test data"""
        np.random.seed(42)
        dates = pd.date_range('2020-01-01', periods=1000, freq='H')
        
        self.test_data = pd.DataFrame({
            'load_1': 50 + 20 * np.sin(2 * np.pi * np.arange(1000) / 24) + np.random.normal(0, 5, 1000),
            'load_2': 60 + 15 * np.sin(2 * np.pi * np.arange(1000) / 24) + np.random.normal(0, 3, 1000),
            'temperature': 20 + 10 * np.sin(2 * np.pi * np.arange(1000) / (24*365)) + np.random.normal(0, 2, 1000),
            'humidity': 50 + 20 * np.random.random(1000),
            'hour': dates.hour,
            'day_of_week': dates.dayofweek
        }, index=dates)
    
    def test_dataset_creation(self):
        """Test dataset creation and sequence generation"""
        dataset = SmartGridDataset(self.test_data, lookback=168, horizon=24)
        
        self.assertGreater(len(dataset), 0)
        self.assertEqual(len(dataset), len(self.test_data) - 168 - 24 + 1)
        
        # Test sample retrieval
        sample = dataset[0]
        self.assertIn('load_input', sample)
        self.assertIn('metadata_input', sample)
        self.assertIn('target', sample)
        
        # Check tensor shapes
        self.assertEqual(sample['load_input'].shape, (168, 2))  # 2 load columns
        self.assertEqual(sample['metadata_input'].shape, (168, 4))  # 4 metadata columns
        self.assertEqual(sample['target'].shape, (24, 2))  # 24-hour horizon, 2 loads

class TestSmartGridMetrics(unittest.TestCase):
    """Test SmartGridMetrics functionality"""
    
    def setUp(self):
        """Create test predictions and targets"""
        np.random.seed(42)
        self.y_true = np.random.randn(100, 3) + 50  # 100 samples, 3 loads
        self.y_pred = self.y_true + np.random.randn(100, 3) * 0.1  # Add small error
    
    def test_basic_metrics(self):
        """Test basic forecasting metrics"""
        rmse = SmartGridMetrics.rmse(self.y_true, self.y_pred)
        mae = SmartGridMetrics.mae(self.y_true, self.y_pred)
        mape = SmartGridMetrics.mape(self.y_true, self.y_pred)
        
        self.assertGreater(rmse, 0)
        self.assertGreater(mae, 0)
        self.assertGreater(mape, 0)
        self.assertLess(rmse, 1.0)  # Should be small due to small added error
    
    def test_smart_grid_metrics(self):
        """Test smart grid-specific metrics"""
        peak_error = SmartGridMetrics.peak_load_error(self.y_true, self.y_pred)
        energy_error = SmartGridMetrics.energy_balance_error(self.y_true, self.y_pred)
        
        self.assertGreater(peak_error, 0)
        self.assertGreater(energy_error, 0)
        self.assertLess(peak_error, 100)  # Should be reasonable percentage
    
    def test_compute_all_metrics(self):
        """Test comprehensive metrics computation"""
        metrics = SmartGridMetrics.compute_all_metrics(self.y_true, self.y_pred)
        
        expected_metrics = ['rmse', 'mae', 'mape', 'peak_load_error', 'energy_balance_error']
        for metric in expected_metrics:
            self.assertIn(metric, metrics)
            self.assertGreater(metrics[metric], 0)

class TestConceptDriftSimulator(unittest.TestCase):
    """Test ConceptDriftSimulator functionality"""
    
    def setUp(self):
        """Create test data"""
        np.random.seed(42)
        dates = pd.date_range('2020-01-01', periods=1000, freq='H')
        
        self.test_data = pd.DataFrame({
            'load_1': 50 + np.random.normal(0, 5, 1000),
            'temperature': 20 + np.random.normal(0, 2, 1000),
            'humidity': 50 + np.random.normal(0, 5, 1000)
        }, index=dates)
    
    def test_seasonal_transition(self):
        """Test seasonal transition drift simulation"""
        modified_data = ConceptDriftSimulator.seasonal_transition(
            self.test_data, transition_point=500, intensity=0.2
        )
        
        # Check that data was modified
        self.assertFalse(modified_data.equals(self.test_data))
        
        # Check that temperature was affected
        temp_diff = modified_data['temperature'].iloc[800:].mean() - self.test_data['temperature'].iloc[800:].mean()
        self.assertGreater(abs(temp_diff), 0.1)
    
    def test_demand_response_event(self):
        """Test demand response event simulation"""
        modified_data = ConceptDriftSimulator.demand_response_event(
            self.test_data, event_start=500, event_duration=24, reduction=0.15
        )
        
        # Check that load was reduced during event
        original_load = self.test_data['load_1'].iloc[500:524].mean()
        modified_load = modified_data['load_1'].iloc[500:524].mean()
        self.assertLess(modified_load, original_load)
    
    def test_extreme_weather(self):
        """Test extreme weather event simulation"""
        modified_data = ConceptDriftSimulator.extreme_weather(
            self.test_data, event_start=500, event_duration=72, intensity=2.0
        )
        
        # Check that both temperature and load were affected
        temp_diff = modified_data['temperature'].iloc[500:572].mean() - self.test_data['temperature'].iloc[500:572].mean()
        load_diff = modified_data['load_1'].iloc[500:572].mean() - self.test_data['load_1'].iloc[500:572].mean()
        
        self.assertGreater(abs(temp_diff), 1.0)
        self.assertGreater(load_diff, 0)  # Load should increase

class TestCLEAREModel(unittest.TestCase):
    """Test CLEAR-E model components"""
    
    def setUp(self):
        """Set up test configuration"""
        self.config = {
            'input_dim': 168 * 2,  # 1 week * 2 loads
            'metadata_dim': 4,
            'output_dim': 2,
            'horizon': 24,
            'hidden_dim': 64,
            'concept_dim': 32,
            'bottleneck_dim': 16,
            'memory_size': 5,
            'momentum': 0.9,
            'penalty_weight': 1.4
        }
        
        self.batch_size = 8
        self.seq_len = 168
    
    def test_concept_encoder(self):
        """Test energy-specific concept encoder"""
        encoder = EnergySpecificConceptEncoder(
            input_dim=self.config['input_dim'],
            metadata_dim=self.config['metadata_dim'],
            concept_dim=self.config['concept_dim']
        )
        
        # Test forward pass
        temporal_input = torch.randn(self.batch_size, self.seq_len, 2)
        metadata_input = torch.randn(self.batch_size, self.seq_len, 4)
        
        concept_vector = encoder(temporal_input, metadata_input)
        
        self.assertEqual(concept_vector.shape, (self.batch_size, self.config['concept_dim']))
        self.assertFalse(torch.isnan(concept_vector).any())
    
    def test_adaptation_generator(self):
        """Test lightweight adaptation generator"""
        target_layers = [('layer1', 128), ('layer2', 64)]
        generator = LightweightAdaptationGenerator(
            concept_dim=self.config['concept_dim'],
            target_layers=target_layers,
            bottleneck_dim=self.config['bottleneck_dim']
        )
        
        # Test forward pass
        drift_vector = torch.randn(self.batch_size, self.config['concept_dim'])
        adaptations = generator(drift_vector)
        
        self.assertEqual(len(adaptations), 2)
        self.assertIn('layer1', adaptations)
        self.assertIn('layer2', adaptations)
        self.assertEqual(adaptations['layer1'].shape, (self.batch_size, 128))
    
    def test_clear_e_model(self):
        """Test complete CLEAR-E model"""
        model = CLEAR_E(self.config)
        
        # Test forward pass
        temporal_input = torch.randn(self.batch_size, self.seq_len, 2)
        metadata_input = torch.randn(self.batch_size, self.seq_len, 4)
        
        outputs = model(temporal_input, metadata_input)
        
        # Check outputs
        self.assertIn('predictions', outputs)
        self.assertIn('concept_vector', outputs)
        self.assertIn('drift_vector', outputs)
        
        predictions = outputs['predictions']
        self.assertEqual(predictions.shape, (self.batch_size, 24, 2))
        self.assertFalse(torch.isnan(predictions).any())

class TestBaselineModels(unittest.TestCase):
    """Test baseline model implementations"""
    
    def setUp(self):
        """Set up test configuration"""
        self.config = {
            'input_dim': 2,
            'seq_len': 168,
            'output_dim': 2,
            'horizon': 24,
            'hidden_dim': 64
        }
        
        self.batch_size = 8
        self.seq_len = 168
    
    def test_lstm_model(self):
        """Test LSTM baseline model"""
        model = LSTMModel(
            input_dim=self.config['input_dim'],
            hidden_dim=self.config['hidden_dim'],
            output_dim=self.config['output_dim'],
            horizon=self.config['horizon']
        )
        
        # Test forward pass
        x = torch.randn(self.batch_size, self.seq_len, self.config['input_dim'])
        output = model(x)
        
        self.assertEqual(output.shape, (self.batch_size, 24, 2))
        self.assertFalse(torch.isnan(output).any())
    
    def test_transformer_model(self):
        """Test Transformer baseline model"""
        model = TransformerModel(
            input_dim=self.config['input_dim'],
            d_model=64,
            output_dim=self.config['output_dim'],
            horizon=self.config['horizon']
        )
        
        # Test forward pass
        x = torch.randn(self.batch_size, self.seq_len, self.config['input_dim'])
        output = model(x)
        
        self.assertEqual(output.shape, (self.batch_size, 24, 2))
        self.assertFalse(torch.isnan(output).any())

class TestExperimentalFramework(unittest.TestCase):
    """Test experimental framework functionality"""
    
    def setUp(self):
        """Set up test framework"""
        self.config = {
            'lookback': 168,
            'horizon': 24,
            'n_runs': 2,
            'test_split': 0.2,
            'val_split': 0.2
        }
        
        self.framework = ExperimentalFramework(self.config)
        
        # Create temporary directory for results
        self.temp_dir = tempfile.mkdtemp()
        self.original_cwd = os.getcwd()
        os.chdir(self.temp_dir)
    
    def tearDown(self):
        """Clean up temporary directory"""
        os.chdir(self.original_cwd)
        shutil.rmtree(self.temp_dir)
    
    def test_dataset_loading(self):
        """Test dataset loading and preparation"""
        self.framework.load_datasets()
        
        self.assertGreater(len(self.framework.datasets), 0)
        
        # Check that datasets have required structure
        for dataset_name, dataset in self.framework.datasets.items():
            self.assertIsInstance(dataset, pd.DataFrame)
            self.assertGreater(len(dataset), 100)  # Reasonable size
            
            # Check for load columns
            load_cols = [col for col in dataset.columns if 'load' in col.lower()]
            self.assertGreater(len(load_cols), 0)
    
    def test_synthetic_dataset_creation(self):
        """Test synthetic dataset creation"""
        dataset = self.framework._create_synthetic_dataset('ECL')
        
        self.assertIsInstance(dataset, pd.DataFrame)
        self.assertEqual(len(dataset), 17520)  # 2 years hourly
        
        # Check required columns
        required_cols = ['temperature', 'humidity', 'wind_speed', 'solar_radiation', 'hour', 'day_of_week']
        for col in required_cols:
            self.assertIn(col, dataset.columns)
        
        # Check load columns
        load_cols = [col for col in dataset.columns if 'load' in col.lower()]
        self.assertGreater(len(load_cols), 0)

def run_tests():
    """Run all tests"""
    print("Running CLEAR-E Experimental Framework Tests...")
    print("=" * 60)
    
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add test cases
    test_classes = [
        TestSmartGridDataset,
        TestSmartGridMetrics,
        TestConceptDriftSimulator,
        TestCLEAREModel,
        TestBaselineModels,
        TestExperimentalFramework
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # Print summary
    print("\n" + "=" * 60)
    if result.wasSuccessful():
        print("✅ All tests passed successfully!")
        print(f"Ran {result.testsRun} tests with 0 failures and 0 errors.")
    else:
        print("❌ Some tests failed!")
        print(f"Ran {result.testsRun} tests with {len(result.failures)} failures and {len(result.errors)} errors.")
        
        if result.failures:
            print("\nFailures:")
            for test, traceback in result.failures:
                print(f"  - {test}: {traceback}")
        
        if result.errors:
            print("\nErrors:")
            for test, traceback in result.errors:
                print(f"  - {test}: {traceback}")
    
    return result.wasSuccessful()

if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)
