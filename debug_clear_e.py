#!/usr/bin/env python3
"""
Debug script for CLEAR-E target layer detection
"""

import torch
import torch.nn as nn
import argparse
import sys
import os

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from adapter.clear_e import ClearE, add_adapters_
from adapter.module.base import Adaptation
from models.DLinear import Model as DLinear


def create_debug_args():
    """Create test arguments"""
    args = argparse.Namespace()
    
    # Basic model args
    args.seq_len = 60
    args.pred_len = 24
    args.enc_in = 7
    args.c_out = 7
    args.individual = False
    
    # CLEAR-E specific args
    args.concept_dim = 64
    args.bottleneck_dim = 32
    args.metadata_dim = 10
    args.metadata_hidden_dim = 32
    args.drift_memory_size = 10
    args.drift_reg_weight = 0.1
    args.target_layers = ['Linear']
    
    # Training args
    args.freeze = True
    args.merge_weights = 1
    args.tune_mode = 'down_up'
    args.act = 'identity'
    args.ema = 0.9
    args.do_predict = False
    args.wo_clip = False
    
    # PROCEED specific args
    args.individual_generator = False
    args.shared_generator = True
    
    return args


def debug_target_layers():
    """Debug target layer detection"""
    print("🔍 Debugging CLEAR-E Target Layer Detection")
    print("=" * 50)
    
    args = create_debug_args()
    
    # Create backbone
    backbone = DLinear(args)
    print(f"📋 Original DLinear layers:")
    for name, module in backbone.named_modules():
        if isinstance(module, nn.Linear):
            print(f"   {name}: {type(module).__name__} {module.weight.shape}")
    
    # Add adapters
    backbone_with_adapters = add_adapters_(backbone, args)
    print(f"\n📋 After adding adapters:")
    
    adaptation_modules = []
    for name, module in backbone_with_adapters.named_modules():
        if isinstance(module, Adaptation):
            adaptation_modules.append((name, module))
            print(f"   {name}: {type(module).__name__}")
    
    print(f"\n📊 Found {len(adaptation_modules)} adaptation modules")
    
    # Test target layer matching
    target_layers = ['Linear', 'linear', 'projection', 'head', 'output', 'fc']
    print(f"\n🎯 Testing target layer patterns: {target_layers}")
    
    matched_layers = []
    for name, module in adaptation_modules:
        layer_name = name.split('.')[-1].lower()
        print(f"   Checking '{name}' -> layer_name='{layer_name}'")
        
        for target in target_layers:
            if target.lower() in layer_name:
                matched_layers.append((name, target))
                print(f"     ✅ Matches target '{target}'")
                break
        else:
            print(f"     ❌ No match found")
    
    print(f"\n📈 Matched {len(matched_layers)} target layers:")
    for name, target in matched_layers:
        print(f"   {name} (matched '{target}')")
    
    # Test with different target patterns
    print(f"\n🔧 Testing alternative target patterns...")
    
    # Try matching any Linear layer
    linear_matches = []
    for name, module in adaptation_modules:
        if 'linear' in name.lower() or name.split('.')[-1].lower() == 'linear':
            linear_matches.append(name)
    
    print(f"   Linear pattern matches: {len(linear_matches)}")
    for name in linear_matches:
        print(f"     {name}")
    
    # Try matching by module type in the path
    type_matches = []
    for name, module in adaptation_modules:
        # Check if the original module was a Linear layer
        try:
            original_module = backbone_with_adapters.get_submodule(name.replace('_adaptation', ''))
            if isinstance(original_module, nn.Linear):
                type_matches.append(name)
        except:
            pass
    
    print(f"   Type-based matches: {len(type_matches)}")
    for name in type_matches:
        print(f"     {name}")
    
    return matched_layers, linear_matches, type_matches


def test_lightweight_generator():
    """Test the lightweight generator with different target patterns"""
    print("\n🧪 Testing LightweightAdaptGenerator")
    print("=" * 50)
    
    args = create_debug_args()
    backbone = DLinear(args)
    backbone_with_adapters = add_adapters_(backbone, args)
    
    from adapter.clear_e import LightweightAdaptGenerator
    
    # Test with different target layer patterns
    test_patterns = [
        ['Linear'],
        ['linear'],
        ['Linear', 'linear'],
        ['projection', 'head', 'output', 'fc'],
        [],  # Empty - should match nothing
    ]
    
    for i, pattern in enumerate(test_patterns):
        print(f"\n🎯 Test {i+1}: target_layers = {pattern}")
        
        try:
            generator = LightweightAdaptGenerator(
                backbone=backbone_with_adapters,
                concept_features=args.concept_dim,
                mid_dim=args.bottleneck_dim,
                target_layers=pattern
            )
            
            print(f"   ✅ Generator created successfully")
            print(f"   📊 dim_name_dict: {dict(generator.dim_name_dict)}")
            print(f"   🔧 bottlenecks: {len(generator.bottlenecks)} modules")
            
            # Test forward pass
            concept = torch.randn(2, args.concept_dim)
            adaptations = generator(concept)
            print(f"   🚀 Forward pass successful: {len(adaptations)} adaptations")
            
        except Exception as e:
            print(f"   ❌ Error: {e}")


def main():
    """Run debugging"""
    matched, linear, type_based = debug_target_layers()
    test_lightweight_generator()
    
    print(f"\n💡 Recommendations:")
    if len(matched) == 0:
        print(f"   • No layers matched with current patterns")
        if len(linear) > 0:
            print(f"   • Try using target_layers=['linear'] or similar")
        if len(type_based) > 0:
            print(f"   • Consider type-based matching")
        print(f"   • Check if adapters are being added correctly")
    else:
        print(f"   • Found {len(matched)} matching layers - should work!")
    
    return matched, linear, type_based


if __name__ == "__main__":
    results = main()
