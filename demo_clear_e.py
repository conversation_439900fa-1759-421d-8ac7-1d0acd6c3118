#!/usr/bin/env python3
"""
Comprehensive CLEAR-E Demo
Demonstrates CLEAR-E with synthetic energy data including metadata
"""

import torch
import torch.nn as nn
import numpy as np
import matplotlib.pyplot as plt
import pandas as pd
import argparse
import sys
import os
from datetime import datetime, timedelta

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from adapter.clear_e import ClearE, EnergyAwareLoss
from adapter.proceed import Proceed
from models.DLinear import Model as DLinear
from util.metrics import metric


def generate_synthetic_energy_data(n_samples=1000, seq_len=60, pred_len=24, n_features=7):
    """Generate synthetic energy load data with realistic patterns"""
    
    # Create time index
    start_date = datetime(2023, 1, 1)
    dates = [start_date + timedelta(hours=i) for i in range(n_samples + seq_len + pred_len)]
    
    # Base load pattern (daily and weekly cycles)
    hours = np.array([d.hour for d in dates])
    days = np.array([d.weekday() for d in dates])
    
    # Daily pattern (higher during day, lower at night)
    daily_pattern = 0.3 * np.sin(2 * np.pi * hours / 24 - np.pi/2) + 0.7
    
    # Weekly pattern (higher on weekdays)
    weekly_pattern = np.where(days < 5, 1.1, 0.9)  # Weekday vs weekend
    
    # Seasonal pattern (higher in summer/winter)
    day_of_year = np.array([d.timetuple().tm_yday for d in dates])
    seasonal_pattern = 0.2 * np.cos(2 * np.pi * day_of_year / 365) + 1.0
    
    # Weather influence (temperature affects load)
    temperature = 20 + 10 * np.sin(2 * np.pi * day_of_year / 365) + np.random.normal(0, 3, len(dates))
    temp_effect = 1 + 0.02 * np.abs(temperature - 22)  # Higher load when temp deviates from 22°C
    
    # Combine patterns
    base_load = daily_pattern * weekly_pattern * seasonal_pattern * temp_effect
    
    # Add noise and create multiple features
    data = np.zeros((len(dates), n_features))
    for i in range(n_features):
        noise = np.random.normal(0, 0.1, len(dates))
        feature_variation = 1 + 0.1 * np.sin(2 * np.pi * i / n_features)
        data[:, i] = base_load * feature_variation + noise
    
    # Create metadata features
    metadata = np.column_stack([
        temperature,                                    # Temperature
        50 + 20 * np.random.normal(0, 1, len(dates)),  # Humidity
        np.maximum(0, np.random.normal(5, 3, len(dates))),  # Wind speed
        np.maximum(0, 1000 * np.sin(2 * np.pi * hours / 24) * (hours >= 6) * (hours <= 18)),  # Solar radiation
        hours / 24,                                     # Hour of day (normalized)
        days / 7,                                       # Day of week (normalized)
        (day_of_year % 30) / 30,                       # Day of month (normalized)
        np.random.binomial(1, 0.1, len(dates)),       # Holiday indicator
        (days >= 5).astype(float),                     # Weekend indicator
        np.random.normal(0.5, 0.1, len(dates))        # Random feature
    ])
    
    return data, metadata, dates


def create_sequences(data, metadata, seq_len, pred_len):
    """Create sequences for training/testing"""
    sequences = []
    metadata_sequences = []
    targets = []
    
    for i in range(len(data) - seq_len - pred_len + 1):
        # Input sequence
        seq = data[i:i + seq_len]
        meta_seq = metadata[i:i + seq_len]
        
        # Target sequence
        target = data[i + seq_len:i + seq_len + pred_len]
        
        sequences.append(seq)
        metadata_sequences.append(meta_seq)
        targets.append(target)
    
    return np.array(sequences), np.array(metadata_sequences), np.array(targets)


def create_args():
    """Create arguments for models"""
    args = argparse.Namespace()
    
    # Basic model args
    args.seq_len = 60
    args.pred_len = 24
    args.enc_in = 7
    args.c_out = 7
    args.individual = False
    
    # CLEAR-E specific args
    args.concept_dim = 64
    args.bottleneck_dim = 32
    args.metadata_dim = 10
    args.metadata_hidden_dim = 32
    args.drift_memory_size = 10
    args.drift_reg_weight = 0.1
    args.target_layers = ['linear']  # lowercase to match DLinear layer names
    
    # Training args
    args.freeze = True
    args.merge_weights = 1
    args.tune_mode = 'down_up'
    args.act = 'identity'
    args.ema = 0.9
    args.do_predict = False
    args.wo_clip = False

    # PROCEED specific args
    args.individual_generator = False
    args.shared_generator = True
    
    # Energy-aware loss
    args.use_energy_loss = True
    args.high_load_threshold = 0.8
    args.underestimate_penalty = 2.0
    
    return args


def train_model(model, train_data, train_metadata, train_targets, criterion, optimizer, epochs=10):
    """Simple training loop"""
    model.train()
    losses = []
    
    for epoch in range(epochs):
        epoch_loss = 0
        for i in range(0, len(train_data), 32):  # Batch size 32
            batch_end = min(i + 32, len(train_data))
            
            x = torch.FloatTensor(train_data[i:batch_end])
            metadata = torch.FloatTensor(train_metadata[i:batch_end].mean(axis=1))  # Average over sequence
            y = torch.FloatTensor(train_targets[i:batch_end])
            
            optimizer.zero_grad()
            
            # Forward pass
            if hasattr(model, 'forward') and 'metadata' in model.forward.__code__.co_varnames:
                outputs = model(x, metadata=metadata)
            else:
                outputs = model(x)
            
            loss = criterion(outputs, y)
            
            # Add drift regularization for CLEAR-E
            if hasattr(model, 'get_drift_regularization_loss') and hasattr(model, '_last_drift'):
                drift_loss = model.get_drift_regularization_loss(model._last_drift)
                loss = loss + drift_loss
            
            loss.backward()
            optimizer.step()
            
            epoch_loss += loss.item()
        
        avg_loss = epoch_loss / (len(train_data) // 32 + 1)
        losses.append(avg_loss)
        
        if epoch % 2 == 0:
            print(f"Epoch {epoch}, Loss: {avg_loss:.6f}")
    
    return losses


def evaluate_model(model, test_data, test_metadata, test_targets):
    """Evaluate model performance"""
    model.eval()
    predictions = []
    
    with torch.no_grad():
        for i in range(len(test_data)):
            x = torch.FloatTensor(test_data[i:i+1])
            metadata = torch.FloatTensor(test_metadata[i:i+1].mean(axis=1))
            
            if hasattr(model, 'forward') and 'metadata' in model.forward.__code__.co_varnames:
                pred = model(x, metadata=metadata)
            else:
                pred = model(x)
            
            predictions.append(pred.numpy())
    
    predictions = np.concatenate(predictions, axis=0)
    
    # Calculate metrics
    mse = np.mean((predictions - test_targets) ** 2)
    mae = np.mean(np.abs(predictions - test_targets))
    
    return predictions, mse, mae


def main():
    """Run the comprehensive demo"""
    print("🚀 CLEAR-E Comprehensive Demo")
    print("=" * 50)
    
    # Set random seeds for reproducibility
    torch.manual_seed(42)
    np.random.seed(42)
    
    # Generate synthetic data
    print("📊 Generating synthetic energy data...")
    args = create_args()
    data, metadata, dates = generate_synthetic_energy_data(
        n_samples=800, 
        seq_len=args.seq_len, 
        pred_len=args.pred_len,
        n_features=args.enc_in
    )
    
    # Create sequences
    sequences, metadata_sequences, targets = create_sequences(
        data, metadata, args.seq_len, args.pred_len
    )
    
    # Split data
    train_size = int(0.7 * len(sequences))
    val_size = int(0.15 * len(sequences))
    
    train_data = sequences[:train_size]
    train_metadata = metadata_sequences[:train_size]
    train_targets = targets[:train_size]
    
    val_data = sequences[train_size:train_size + val_size]
    val_metadata = metadata_sequences[train_size:train_size + val_size]
    val_targets = targets[train_size:train_size + val_size]
    
    test_data = sequences[train_size + val_size:]
    test_metadata = metadata_sequences[train_size + val_size:]
    test_targets = targets[train_size + val_size:]
    
    print(f"📈 Data split: Train={len(train_data)}, Val={len(val_data)}, Test={len(test_data)}")
    
    # Create models
    print("\n🏗️  Creating models...")
    
    # PROCEED model
    backbone_proceed = DLinear(args)
    proceed_model = Proceed(backbone_proceed, args)
    
    # CLEAR-E model
    backbone_clear_e = DLinear(args)
    clear_e_model = ClearE(backbone_clear_e, args)
    
    print(f"✅ PROCEED model created with {sum(p.numel() for p in proceed_model.parameters())} parameters")
    print(f"✅ CLEAR-E model created with {sum(p.numel() for p in clear_e_model.parameters())} parameters")
    
    # Training setup
    criterion_proceed = nn.MSELoss()
    criterion_clear_e = EnergyAwareLoss(
        base_criterion=nn.MSELoss(),
        high_load_threshold=args.high_load_threshold,
        underestimate_penalty=args.underestimate_penalty
    )
    
    optimizer_proceed = torch.optim.Adam(proceed_model.parameters(), lr=0.001)
    optimizer_clear_e = torch.optim.Adam(clear_e_model.parameters(), lr=0.001)
    
    # Train models
    print("\n🎯 Training PROCEED model...")
    proceed_losses = train_model(
        proceed_model, train_data, train_metadata, train_targets, 
        criterion_proceed, optimizer_proceed, epochs=10
    )
    
    print("\n🎯 Training CLEAR-E model...")
    clear_e_losses = train_model(
        clear_e_model, train_data, train_metadata, train_targets,
        criterion_clear_e, optimizer_clear_e, epochs=10
    )
    
    # Evaluate models
    print("\n📊 Evaluating models...")
    
    proceed_preds, proceed_mse, proceed_mae = evaluate_model(
        proceed_model, test_data, test_metadata, test_targets
    )
    
    clear_e_preds, clear_e_mse, clear_e_mae = evaluate_model(
        clear_e_model, test_data, test_metadata, test_targets
    )
    
    # Results
    print("\n📈 Results:")
    print(f"PROCEED  - MSE: {proceed_mse:.6f}, MAE: {proceed_mae:.6f}")
    print(f"CLEAR-E  - MSE: {clear_e_mse:.6f}, MAE: {clear_e_mae:.6f}")
    
    improvement_mse = (proceed_mse - clear_e_mse) / proceed_mse * 100
    improvement_mae = (proceed_mae - clear_e_mae) / proceed_mae * 100
    
    print(f"\n🎉 CLEAR-E Improvements:")
    print(f"MSE: {improvement_mse:+.2f}%")
    print(f"MAE: {improvement_mae:+.2f}%")
    
    # Save results
    results = {
        'proceed_mse': proceed_mse,
        'proceed_mae': proceed_mae,
        'clear_e_mse': clear_e_mse,
        'clear_e_mae': clear_e_mae,
        'improvement_mse': improvement_mse,
        'improvement_mae': improvement_mae
    }
    
    print(f"\n💾 Demo completed successfully!")
    print(f"🔍 Key insights:")
    print(f"   • CLEAR-E uses energy-specific metadata for better concept understanding")
    print(f"   • Lightweight adaptation reduces computational overhead")
    print(f"   • Drift memory provides smoother adaptation over time")
    print(f"   • Energy-aware loss penalizes underestimation during high-load periods")
    
    return results


if __name__ == "__main__":
    results = main()
