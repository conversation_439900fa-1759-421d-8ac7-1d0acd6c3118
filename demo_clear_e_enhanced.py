#!/usr/bin/env python3
"""
Enhanced CLEAR-E Demo with Advanced Features
Demonstrates improved CLEAR-E with better hyperparameters and monitoring
"""

import torch
import torch.nn as nn
import numpy as np
import matplotlib.pyplot as plt
import pandas as pd
import argparse
import sys
import os
from datetime import datetime, timedelta

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from adapter.clear_e import ClearE, EnergyAwareLoss
from adapter.proceed import Proceed
from models.DLinear import Model as DLinear


def create_enhanced_args():
    """Create enhanced arguments for CLEAR-E"""
    args = argparse.Namespace()
    
    # Basic model args
    args.seq_len = 60
    args.pred_len = 24
    args.enc_in = 7
    args.c_out = 7
    args.individual = False
    
    # Enhanced CLEAR-E specific args
    args.concept_dim = 64
    args.bottleneck_dim = 32
    args.metadata_dim = 10
    args.metadata_hidden_dim = 64  # Increased
    args.drift_memory_size = 15    # Increased
    args.drift_reg_weight = 0.05   # Reduced for less aggressive regularization
    args.target_layers = ['linear']  # lowercase to match DLinear layer names
    
    # Enhanced metadata processing
    args.use_metadata_attention = True
    args.metadata_feature_groups = ['weather', 'temporal', 'calendar']
    
    # Enhanced drift memory
    args.adaptive_memory = True
    args.drift_threshold = 0.3  # More sensitive drift detection
    
    # Training args
    args.freeze = True
    args.merge_weights = 1
    args.tune_mode = 'down_up'
    args.act = 'identity'
    args.ema = 0.95  # Higher EMA for more stability
    args.do_predict = False
    args.wo_clip = False
    
    # PROCEED specific args
    args.individual_generator = False
    args.shared_generator = True
    
    # Enhanced energy-aware loss
    args.use_energy_loss = True
    args.high_load_threshold = 0.75  # Slightly lower threshold
    args.underestimate_penalty = 1.3  # More moderate penalty
    
    return args


def generate_enhanced_energy_data(n_samples=1200, seq_len=60, pred_len=24, n_features=7):
    """Generate more realistic synthetic energy data with concept drift"""
    
    # Create time index
    start_date = datetime(2023, 1, 1)
    dates = [start_date + timedelta(hours=i) for i in range(n_samples + seq_len + pred_len)]
    
    # Time features
    hours = np.array([d.hour for d in dates])
    days = np.array([d.weekday() for d in dates])
    day_of_year = np.array([d.timetuple().tm_yday for d in dates])
    
    # Base patterns with concept drift
    time_progress = np.arange(len(dates)) / len(dates)
    
    # Evolving daily pattern (concept drift)
    daily_base = 0.3 * np.sin(2 * np.pi * hours / 24 - np.pi/2) + 0.7
    daily_drift = 0.1 * np.sin(2 * np.pi * time_progress * 3)  # 3 cycles of drift
    daily_pattern = daily_base + daily_drift
    
    # Weekly pattern with seasonal changes
    weekly_pattern = np.where(days < 5, 1.1, 0.9)
    seasonal_weekly = 0.05 * np.sin(2 * np.pi * day_of_year / 365)
    weekly_pattern += seasonal_weekly
    
    # Seasonal pattern
    seasonal_pattern = 0.25 * np.cos(2 * np.pi * day_of_year / 365) + 1.0
    
    # Weather influence with realistic correlations
    temperature = 20 + 15 * np.sin(2 * np.pi * day_of_year / 365) + np.random.normal(0, 4, len(dates))
    humidity = 50 + 20 * np.sin(2 * np.pi * day_of_year / 365 + np.pi/4) + np.random.normal(0, 10, len(dates))
    wind_speed = np.maximum(0, 8 + 5 * np.sin(2 * np.pi * day_of_year / 365 + np.pi/2) + np.random.normal(0, 3, len(dates)))
    
    # Temperature effect on load (cooling/heating)
    temp_effect = 1 + 0.03 * np.abs(temperature - 22)
    
    # Humidity effect (slight)
    humidity_effect = 1 + 0.005 * (humidity - 50)
    
    # Wind effect (renewable generation offset)
    wind_effect = 1 - 0.01 * wind_speed
    
    # Combine all effects
    base_load = daily_pattern * weekly_pattern * seasonal_pattern * temp_effect * humidity_effect * wind_effect
    
    # Add concept drift events (sudden changes)
    drift_points = [len(dates) // 3, 2 * len(dates) // 3]
    for drift_point in drift_points:
        if drift_point < len(dates):
            # Sudden shift in load pattern
            shift_magnitude = np.random.normal(0.1, 0.05)
            base_load[drift_point:] += shift_magnitude
    
    # Create multiple correlated features
    data = np.zeros((len(dates), n_features))
    for i in range(n_features):
        noise = np.random.normal(0, 0.08, len(dates))
        feature_correlation = 0.8 + 0.4 * np.sin(2 * np.pi * i / n_features)
        data[:, i] = base_load * feature_correlation + noise
    
    # Enhanced metadata features
    solar_radiation = np.maximum(0, 800 * np.sin(2 * np.pi * hours / 24) * 
                                 (hours >= 6) * (hours <= 18) * 
                                 (1 + 0.3 * np.sin(2 * np.pi * day_of_year / 365)))
    
    metadata = np.column_stack([
        temperature,                                    # 0: Temperature
        humidity,                                       # 1: Humidity  
        wind_speed,                                     # 2: Wind speed
        solar_radiation,                               # 3: Solar radiation
        np.sin(2 * np.pi * hours / 24),               # 4: Hour sin
        np.cos(2 * np.pi * hours / 24),               # 5: Hour cos
        np.sin(2 * np.pi * days / 7),                 # 6: Day sin
        np.cos(2 * np.pi * days / 7),                 # 7: Day cos
        np.random.binomial(1, 0.08, len(dates)),      # 8: Holiday indicator
        (days >= 5).astype(float),                     # 9: Weekend indicator
    ])
    
    return data, metadata, dates


def train_with_monitoring(model, train_data, train_metadata, train_targets, 
                         criterion, optimizer, epochs=15, model_name="Model"):
    """Enhanced training with monitoring"""
    model.train()
    losses = []
    
    print(f"\n🎯 Training {model_name}...")
    
    for epoch in range(epochs):
        epoch_loss = 0
        batch_count = 0
        
        for i in range(0, len(train_data), 16):  # Smaller batch size
            batch_end = min(i + 16, len(train_data))
            
            x = torch.FloatTensor(train_data[i:batch_end])
            y = torch.FloatTensor(train_targets[i:batch_end])
            
            optimizer.zero_grad()
            
            # Forward pass
            if hasattr(model, 'forward') and 'metadata' in model.forward.__code__.co_varnames:
                metadata = torch.FloatTensor(train_metadata[i:batch_end].mean(axis=1))
                outputs = model(x, metadata=metadata)
            else:
                outputs = model(x)
            
            loss = criterion(outputs, y)
            
            # Add drift regularization for CLEAR-E
            if hasattr(model, 'get_drift_regularization_loss') and hasattr(model, '_last_drift'):
                drift_loss = model.get_drift_regularization_loss(model._last_drift)
                loss = loss + drift_loss
            
            loss.backward()
            
            # Gradient clipping
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            
            optimizer.step()
            
            epoch_loss += loss.item()
            batch_count += 1
        
        avg_loss = epoch_loss / batch_count
        losses.append(avg_loss)
        
        if epoch % 3 == 0:
            print(f"  Epoch {epoch:2d}: Loss = {avg_loss:.6f}")
            
            # Print CLEAR-E specific statistics
            if hasattr(model, 'get_adaptation_statistics'):
                stats = model.get_adaptation_statistics()
                if 'drift_memory' in stats and stats['drift_memory']:
                    drift_stats = stats['drift_memory']
                    print(f"    Drift: avg={drift_stats.get('avg_drift_magnitude', 0):.4f}, "
                          f"detected={drift_stats.get('drift_detected', False)}")
    
    return losses


def main():
    """Run the enhanced demo"""
    print("🚀 Enhanced CLEAR-E Demo with Advanced Features")
    print("=" * 60)
    
    # Set random seeds
    torch.manual_seed(42)
    np.random.seed(42)
    
    # Generate enhanced data
    print("📊 Generating enhanced synthetic energy data...")
    args = create_enhanced_args()
    data, metadata, dates = generate_enhanced_energy_data(
        n_samples=1000, 
        seq_len=args.seq_len, 
        pred_len=args.pred_len,
        n_features=args.enc_in
    )
    
    # Create sequences
    from demo_clear_e import create_sequences
    sequences, metadata_sequences, targets = create_sequences(
        data, metadata, args.seq_len, args.pred_len
    )
    
    # Split data
    train_size = int(0.7 * len(sequences))
    val_size = int(0.15 * len(sequences))
    
    train_data = sequences[:train_size]
    train_metadata = metadata_sequences[:train_size]
    train_targets = targets[:train_size]
    
    test_data = sequences[train_size + val_size:]
    test_metadata = metadata_sequences[train_size + val_size:]
    test_targets = targets[train_size + val_size:]
    
    print(f"📈 Data split: Train={len(train_data)}, Test={len(test_data)}")
    
    # Create models
    print("\n🏗️  Creating enhanced models...")
    
    # PROCEED model
    backbone_proceed = DLinear(args)
    proceed_model = Proceed(backbone_proceed, args)
    
    # Enhanced CLEAR-E model
    backbone_clear_e = DLinear(args)
    clear_e_model = ClearE(backbone_clear_e, args)
    
    print(f"✅ PROCEED: {sum(p.numel() for p in proceed_model.parameters())} parameters")
    print(f"✅ CLEAR-E: {sum(p.numel() for p in clear_e_model.parameters())} parameters")
    
    # Print CLEAR-E statistics
    if hasattr(clear_e_model, 'get_adaptation_statistics'):
        stats = clear_e_model.get_adaptation_statistics()
        print(f"📊 CLEAR-E Adaptation Stats:")
        print(f"   • Target groups: {stats['adaptation_targets']['num_target_groups']}")
        print(f"   • Adapter params: {stats['parameters']['adapter']} ({stats['parameters']['adapter_ratio']:.1%})")
        print(f"   • Metadata params: {stats['parameters']['metadata_encoder']} ({stats['parameters']['metadata_ratio']:.1%})")
    
    # Enhanced training setup
    criterion_proceed = nn.MSELoss()
    criterion_clear_e = EnergyAwareLoss(
        base_criterion=nn.MSELoss(),
        high_load_threshold=args.high_load_threshold,
        underestimate_penalty=args.underestimate_penalty,
        adaptive_penalty=True,
        load_aware_weighting=True
    )
    
    # Use different learning rates
    optimizer_proceed = torch.optim.AdamW(proceed_model.parameters(), lr=0.001, weight_decay=1e-5)
    optimizer_clear_e = torch.optim.AdamW(clear_e_model.parameters(), lr=0.0008, weight_decay=1e-5)
    
    # Train models
    proceed_losses = train_with_monitoring(
        proceed_model, train_data, train_metadata, train_targets, 
        criterion_proceed, optimizer_proceed, epochs=15, model_name="PROCEED"
    )
    
    clear_e_losses = train_with_monitoring(
        clear_e_model, train_data, train_metadata, train_targets,
        criterion_clear_e, optimizer_clear_e, epochs=15, model_name="CLEAR-E"
    )
    
    # Evaluate models
    print("\n📊 Evaluating models...")
    
    from demo_clear_e import evaluate_model
    proceed_preds, proceed_mse, proceed_mae = evaluate_model(
        proceed_model, test_data, test_metadata, test_targets
    )
    
    clear_e_preds, clear_e_mse, clear_e_mae = evaluate_model(
        clear_e_model, test_data, test_metadata, test_targets
    )
    
    # Results
    print("\n📈 Final Results:")
    print(f"PROCEED  - MSE: {proceed_mse:.6f}, MAE: {proceed_mae:.6f}")
    print(f"CLEAR-E  - MSE: {clear_e_mse:.6f}, MAE: {clear_e_mae:.6f}")
    
    improvement_mse = (proceed_mse - clear_e_mse) / proceed_mse * 100
    improvement_mae = (proceed_mae - clear_e_mae) / proceed_mae * 100
    
    print(f"\n🎉 CLEAR-E Performance vs PROCEED:")
    print(f"MSE: {improvement_mse:+.2f}%")
    print(f"MAE: {improvement_mae:+.2f}%")
    
    # Final statistics
    if hasattr(clear_e_model, 'get_adaptation_statistics'):
        final_stats = clear_e_model.get_adaptation_statistics()
        if 'feature_importance' in final_stats:
            importance = final_stats['feature_importance']
            print(f"\n🔍 Learned Feature Importance:")
            feature_names = ['Temp', 'Humidity', 'Wind', 'Solar', 'Hour_sin', 'Hour_cos', 'Day_sin', 'Day_cos', 'Holiday', 'Weekend']
            for i, (name, imp) in enumerate(zip(feature_names, importance)):
                print(f"   {name:10s}: {imp:.3f}")
    
    print(f"\n💾 Enhanced demo completed!")
    
    return {
        'proceed_mse': proceed_mse, 'proceed_mae': proceed_mae,
        'clear_e_mse': clear_e_mse, 'clear_e_mae': clear_e_mae,
        'improvement_mse': improvement_mse, 'improvement_mae': improvement_mae
    }


if __name__ == "__main__":
    results = main()
